using ArchitecturalAI.Models;

namespace ArchitecturalAI;

/// <summary>
/// 简单的测试类，用于验证核心功能
/// </summary>
public static class Tests
{
    public static void RunAllTests()
    {
        Console.WriteLine("运行基础测试...");
        
        TestPoint2D();
        TestBuildingContour();
        TestFloorPlan();
        
        Console.WriteLine("所有测试通过！");
    }

    private static void TestPoint2D()
    {
        var point1 = new Point2D(0, 0);
        var point2 = new Point2D(3, 4);
        var distance = point1.DistanceTo(point2);
        
        if (Math.Abs(distance - 5.0f) > 0.01f)
            throw new Exception("Point2D距离计算错误");
        
        Console.WriteLine("✓ Point2D测试通过");
    }

    private static void TestBuildingContour()
    {
        var contour = new BuildingContour();
        contour.Points.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(10, 0),
            new Point2D(10, 10),
            new Point2D(0, 10)
        });

        var area = contour.CalculateArea();
        if (Math.Abs(area - 100.0f) > 0.01f)
            throw new Exception("BuildingContour面积计算错误");

        var perimeter = contour.CalculatePerimeter();
        if (Math.Abs(perimeter - 40.0f) > 0.01f)
            throw new Exception("BuildingContour周长计算错误");
        
        Console.WriteLine("✓ BuildingContour测试通过");
    }

    private static void TestFloorPlan()
    {
        var floorPlan = new FloorPlan();
        
        var room = new Room
        {
            Name = "测试房间",
            Type = RoomType.LivingRoom
        };
        room.Boundary.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(5, 0),
            new Point2D(5, 4),
            new Point2D(0, 4)
        });

        floorPlan.Rooms.Add(room);

        var totalArea = floorPlan.CalculateTotalArea();
        if (Math.Abs(totalArea - 20.0f) > 0.01f)
            throw new Exception("FloorPlan面积计算错误");

        var stats = floorPlan.GetRoomTypeStatistics();
        if (stats[RoomType.LivingRoom] != 1)
            throw new Exception("FloorPlan统计错误");
        
        Console.WriteLine("✓ FloorPlan测试通过");
    }
}
