@echo off
echo 建筑平面图AI生成系统 - 构建脚本
echo =====================================

echo.
echo 1. 清理项目...
dotnet clean

echo.
echo 2. 还原NuGet包...
dotnet restore

echo.
echo 3. 构建解决方案...
dotnet build --configuration Release --no-restore

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 构建成功完成！
    echo.
    echo 4. 运行测试...
    dotnet test --no-build --configuration Release
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✓ 所有测试通过！
        echo.
        echo 5. 发布应用程序...
        dotnet publish src\ArchitecturalAI.UI\ArchitecturalAI.UI.csproj --configuration Release --output publish\
        
        if %ERRORLEVEL% EQU 0 (
            echo.
            echo ✓ 发布完成！
            echo 应用程序已发布到 publish\ 目录
            echo.
            echo 使用方法:
            echo   cd publish
            echo   ArchitecturalAI.UI.exe
        ) else (
            echo.
            echo ✗ 发布失败！
        )
    ) else (
        echo.
        echo ✗ 测试失败！
    )
) else (
    echo.
    echo ✗ 构建失败！
)

echo.
pause
