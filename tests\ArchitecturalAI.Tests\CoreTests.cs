using ArchitecturalAI.Core.Models;
using Xunit;

namespace ArchitecturalAI.Tests;

public class CoreTests
{
    [Fact]
    public void Point2D_DistanceTo_ShouldCalculateCorrectDistance()
    {
        // Arrange
        var point1 = new Point2D(0, 0);
        var point2 = new Point2D(3, 4);

        // Act
        var distance = point1.DistanceTo(point2);

        // Assert
        Assert.Equal(5.0f, distance, 1);
    }

    [Fact]
    public void BuildingContour_CalculateArea_ShouldReturnCorrectArea()
    {
        // Arrange
        var contour = new BuildingContour();
        contour.Points.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(10, 0),
            new Point2D(10, 10),
            new Point2D(0, 10)
        });

        // Act
        var area = contour.CalculateArea();

        // Assert
        Assert.Equal(100.0f, area, 1);
    }

    [Fact]
    public void BuildingContour_CalculatePerimeter_ShouldReturnCorrectPerimeter()
    {
        // Arrange
        var contour = new BuildingContour();
        contour.Points.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(10, 0),
            new Point2D(10, 10),
            new Point2D(0, 10)
        });

        // Act
        var perimeter = contour.CalculatePerimeter();

        // Assert
        Assert.Equal(40.0f, perimeter, 1);
    }

    [Fact]
    public void BuildingContour_IsClosed_ShouldReturnTrueForClosedContour()
    {
        // Arrange
        var contour = new BuildingContour();
        contour.Points.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(10, 0),
            new Point2D(10, 10),
            new Point2D(0, 10),
            new Point2D(0, 0) // 闭合点
        });

        // Act
        var isClosed = contour.IsClosed();

        // Assert
        Assert.True(isClosed);
    }

    [Fact]
    public void Room_CalculateArea_ShouldReturnCorrectArea()
    {
        // Arrange
        var room = new Room
        {
            Name = "客厅",
            Type = RoomType.LivingRoom
        };
        room.Boundary.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(6, 0),
            new Point2D(6, 4),
            new Point2D(0, 4)
        });

        // Act
        var area = room.CalculateArea();

        // Assert
        Assert.Equal(24.0f, area, 1);
    }

    [Fact]
    public void FloorPlan_CalculateTotalArea_ShouldSumAllRoomAreas()
    {
        // Arrange
        var floorPlan = new FloorPlan();
        
        var livingRoom = new Room
        {
            Name = "客厅",
            Type = RoomType.LivingRoom
        };
        livingRoom.Boundary.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(6, 0),
            new Point2D(6, 4),
            new Point2D(0, 4)
        });

        var bedroom = new Room
        {
            Name = "卧室",
            Type = RoomType.Bedroom
        };
        bedroom.Boundary.AddRange(new[]
        {
            new Point2D(0, 0),
            new Point2D(4, 0),
            new Point2D(4, 3),
            new Point2D(0, 3)
        });

        floorPlan.Rooms.Add(livingRoom);
        floorPlan.Rooms.Add(bedroom);

        // Act
        var totalArea = floorPlan.CalculateTotalArea();

        // Assert
        Assert.Equal(36.0f, totalArea, 1); // 24 + 12
    }

    [Fact]
    public void FloorPlan_GetRoomTypeStatistics_ShouldReturnCorrectCounts()
    {
        // Arrange
        var floorPlan = new FloorPlan();
        floorPlan.Rooms.AddRange(new[]
        {
            new Room { Type = RoomType.LivingRoom },
            new Room { Type = RoomType.Bedroom },
            new Room { Type = RoomType.Bedroom },
            new Room { Type = RoomType.Kitchen },
            new Room { Type = RoomType.Bathroom }
        });

        // Act
        var statistics = floorPlan.GetRoomTypeStatistics();

        // Assert
        Assert.Equal(1, statistics[RoomType.LivingRoom]);
        Assert.Equal(2, statistics[RoomType.Bedroom]);
        Assert.Equal(1, statistics[RoomType.Kitchen]);
        Assert.Equal(1, statistics[RoomType.Bathroom]);
    }

    [Fact]
    public void FloorPlan_Validate_ShouldDetectMissingRoomTypes()
    {
        // Arrange
        var floorPlan = new FloorPlan();
        floorPlan.Rooms.Add(new Room { Type = RoomType.LivingRoom });
        // 缺少其他必要房间类型

        // Act
        var result = floorPlan.Validate();

        // Assert
        Assert.True(result.Warnings.Any(w => w.Contains("卧室")));
        Assert.True(result.Warnings.Any(w => w.Contains("厨房")));
        Assert.True(result.Warnings.Any(w => w.Contains("卫生间")));
    }

    [Fact]
    public void GenerationParameters_DefaultValues_ShouldBeReasonable()
    {
        // Arrange & Act
        var parameters = new GenerationParameters();

        // Assert
        Assert.Equal(3, parameters.RoomCount);
        Assert.Equal(8.0f, parameters.MinRoomArea);
        Assert.True(parameters.IncludeBalcony);
        Assert.False(parameters.IncludeStudy);
        Assert.Equal("Modern", parameters.Style);
    }

    [Fact]
    public void ValidationResult_AddError_ShouldMakeResultInvalid()
    {
        // Arrange
        var result = new ValidationResult();
        Assert.True(result.IsValid); // 初始状态应该是有效的

        // Act
        result.AddError("测试错误");

        // Assert
        Assert.False(result.IsValid);
        Assert.Contains("测试错误", result.Errors);
    }

    [Fact]
    public void ValidationResult_AddWarning_ShouldNotAffectValidity()
    {
        // Arrange
        var result = new ValidationResult();

        // Act
        result.AddWarning("测试警告");

        // Assert
        Assert.True(result.IsValid); // 警告不应该影响有效性
        Assert.Contains("测试警告", result.Warnings);
    }
}
