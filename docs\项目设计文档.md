# 建筑平面图AI生成系统 - 设计文档

## 1. 项目概述

### 1.1 项目背景
建筑设计是一个复杂且耗时的过程，特别是在初期的平面布置阶段。传统的设计方法依赖于建筑师的经验和手工绘制，效率较低且容易受到主观因素影响。本项目旨在利用人工智能技术，特别是深度学习中的生成对抗网络(GAN)，来自动化这一过程。

### 1.2 项目目标
- 开发一个基于C#的桌面应用程序，提供直观的用户界面
- 集成深度学习模型，实现从建筑轮廓到平面布置的自动转换
- 支持参数化控制，允许用户调整生成参数
- 提供完整的数据收集、预处理和模型训练工具链

### 1.3 技术栈
- **前端**: WPF (Windows Presentation Foundation)
- **后端**: .NET 8.0, C#
- **机器学习**: ONNX Runtime, PyTorch (训练)
- **图形处理**: SkiaSharp
- **数据处理**: JSON, CSV

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   业务逻辑层     │    │   数据访问层     │
│   (UI Layer)    │◄──►│ (Business Layer)│◄──►│  (Data Layer)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  WPF Controls   │    │   Core Models   │    │  File System    │
│  Canvas Drawing │    │   ML Services   │    │  JSON Storage   │
│  Data Binding   │    │   Validation    │    │  Image Files    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 模块划分

#### 2.2.1 ArchitecturalAI.Core
核心业务模型和逻辑
- **Models**: 数据模型定义
  - `Point2D`: 二维点
  - `BuildingContour`: 建筑轮廓
  - `FloorPlan`: 平面图
  - `Room`: 房间
  - `Wall`, `Door`, `Window`: 建筑元素
- **Services**: 核心业务服务
- **Interfaces**: 接口定义

#### 2.2.2 ArchitecturalAI.UI
用户界面层
- **Views**: XAML视图文件
  - `MainWindow`: 主窗口
  - `ContourEditor`: 轮廓编辑器
  - `FloorPlanViewer`: 平面图查看器
- **ViewModels**: MVVM视图模型
- **Controls**: 自定义控件
- **Styles**: 样式资源

#### 2.2.3 ArchitecturalAI.ML
机器学习模块
- **Interfaces**: ML服务接口
- **Services**: ONNX模型集成
- **Models**: ML相关数据模型

#### 2.2.4 ArchitecturalAI.Data
数据处理模块
- **Interfaces**: 数据服务接口
- **Services**: 数据收集和预处理
- **Models**: 数据相关模型

## 3. 数据模型设计

### 3.1 核心实体关系
```
BuildingContour (1) ──► (1..n) FloorPlan
FloorPlan (1) ──► (1..n) Room
FloorPlan (1) ──► (0..n) Wall
FloorPlan (1) ──► (0..n) Door
FloorPlan (1) ──► (0..n) Window
```

### 3.2 数据流
```
原始图纸 → 数据收集 → 预处理 → 训练数据
                                    ↓
用户输入轮廓 → 模型推理 ← 训练好的模型
     ↓
生成平面图 → 后处理 → 用户界面显示
```

## 4. 机器学习方案

### 4.1 模型架构
采用条件生成对抗网络(Conditional GAN)架构：

#### 生成器 (Generator)
- **输入**: 建筑轮廓图像 (512x512x3)
- **架构**: U-Net with skip connections
- **输出**: 平面布置图像 (512x512x3)

#### 判别器 (Discriminator)
- **输入**: 轮廓图像 + 平面图像 (512x512x6)
- **架构**: CNN分类器
- **输出**: 真实性概率

### 4.2 训练策略
- **损失函数**: GAN Loss + L1 Loss
- **优化器**: Adam (lr=0.0002, β1=0.5, β2=0.999)
- **数据增强**: 旋转、缩放、翻转、噪声
- **正则化**: Batch Normalization, Dropout

### 4.3 评估指标
- **生成质量**: FID (Fréchet Inception Distance)
- **结构相似性**: SSIM (Structural Similarity Index)
- **用户满意度**: 人工评估

## 5. 用户界面设计

### 5.1 主界面布局
```
┌─────────────────────────────────────────────────────────────┐
│ 菜单栏 (文件、编辑、生成、帮助)                                │
├─────────────┬─────────────────────────────┬─────────────────┤
│   工具面板   │         绘图区域             │    结果面板      │
│             │                             │                │
│ - 绘制工具   │    ┌─────────────────────┐   │ - 预览图       │
│ - 轮廓属性   │    │                     │   │ - 统计信息     │
│ - 生成参数   │    │      画布区域        │   │ - 操作按钮     │
│             │    │                     │   │               │
│             │    └─────────────────────┘   │               │
│             │         工具栏               │               │
└─────────────┴─────────────────────────────┴─────────────────┤
│ 状态栏 (坐标、进度、状态信息)                                  │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 交互流程
1. **轮廓绘制**
   - 选择绘制工具
   - 在画布上点击绘制轮廓
   - 右键完成绘制

2. **参数设置**
   - 调整房间数量
   - 选择设计风格
   - 设置其他参数

3. **生成平面图**
   - 点击生成按钮
   - 显示进度条
   - 展示生成结果

4. **结果处理**
   - 预览生成的平面图
   - 查看统计信息
   - 导出或保存结果

## 6. 数据收集与预处理

### 6.1 数据来源
- **公开数据集**: 建筑图纸数据库
- **CAD文件**: DWG, DXF格式
- **图像文件**: JPG, PNG格式
- **手工标注**: 轮廓-平面图配对

### 6.2 数据预处理流程
1. **格式转换**: 统一转换为PNG格式
2. **尺寸标准化**: 调整为512x512像素
3. **数据清洗**: 移除无效或损坏的文件
4. **数据增强**: 旋转、缩放、翻转等变换
5. **数据集划分**: 训练集70%、验证集20%、测试集10%

### 6.3 质量控制
- **自动检测**: 图像质量、轮廓完整性
- **人工审核**: 关键样本的质量检查
- **统计分析**: 数据分布、类别平衡

## 7. 部署与运维

### 7.1 系统要求
- **操作系统**: Windows 10/11
- **运行时**: .NET 8.0 Runtime
- **硬件**: 4GB RAM, 支持DirectX 11的显卡
- **推荐**: NVIDIA GPU (CUDA支持)

### 7.2 安装部署
- **安装包**: MSI安装程序
- **便携版**: 免安装版本
- **模型文件**: 预训练ONNX模型

### 7.3 性能优化
- **GPU加速**: CUDA/DirectML支持
- **内存管理**: 大图像的流式处理
- **缓存机制**: 模型和结果缓存

## 8. 测试策略

### 8.1 单元测试
- **核心模型**: 数据模型的业务逻辑
- **算法函数**: 几何计算、图像处理
- **服务类**: 业务服务的功能测试

### 8.2 集成测试
- **模型集成**: ONNX模型加载和推理
- **数据流**: 端到端数据处理流程
- **UI集成**: 界面与业务逻辑的集成

### 8.3 用户测试
- **可用性测试**: 界面易用性评估
- **性能测试**: 响应时间和资源占用
- **兼容性测试**: 不同硬件配置的兼容性

## 9. 风险与挑战

### 9.1 技术风险
- **模型性能**: 生成质量可能不稳定
- **数据质量**: 训练数据的质量和数量限制
- **计算资源**: 模型推理的性能要求

### 9.2 业务风险
- **用户接受度**: 生成结果的实用性
- **法律合规**: 建筑设计的规范要求
- **知识产权**: 训练数据的版权问题

### 9.3 应对策略
- **迭代改进**: 持续优化模型和算法
- **用户反馈**: 建立反馈机制改进产品
- **专业咨询**: 与建筑专家合作验证

## 10. 未来规划

### 10.1 功能扩展
- **3D可视化**: 三维平面图展示
- **多层建筑**: 支持多层建筑设计
- **BIM集成**: 与BIM软件的数据交换
- **移动端**: 开发移动应用版本

### 10.2 技术升级
- **模型优化**: 更先进的生成模型
- **云端服务**: 云端训练和推理服务
- **实时协作**: 多用户协作设计
- **AI助手**: 智能设计建议和优化

### 10.3 商业化
- **SaaS服务**: 云端订阅服务
- **企业版**: 面向设计院的企业解决方案
- **API服务**: 为第三方开发者提供API
- **培训服务**: 用户培训和技术支持
