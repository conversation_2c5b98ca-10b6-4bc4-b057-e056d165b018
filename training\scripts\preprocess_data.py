#!/usr/bin/env python3
"""
数据预处理脚本

这个脚本用于预处理建筑图纸数据，包括：
1. 图像格式转换和标准化
2. 数据增强
3. 训练/验证/测试集划分
4. 数据质量检查

使用方法:
    python preprocess_data.py --input_path ./raw_data --output_path ./processed_data

依赖包:
    pip install opencv-python pillow numpy scikit-learn matplotlib
"""

import argparse
import os
import json
import shutil
from pathlib import Path
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataPreprocessor:
    """数据预处理器"""
    
    def __init__(self, input_path, output_path, image_size=512):
        self.input_path = Path(input_path)
        self.output_path = Path(output_path)
        self.image_size = image_size
        
        # 创建输出目录
        self.create_output_directories()
    
    def create_output_directories(self):
        """创建输出目录结构"""
        directories = [
            "train/contours", "train/floorplans", "train/images",
            "val/contours", "val/floorplans", "val/images",
            "test/contours", "test/floorplans", "test/images",
            "statistics", "logs"
        ]
        
        for directory in directories:
            (self.output_path / directory).mkdir(parents=True, exist_ok=True)
        
        logger.info(f"输出目录已创建: {self.output_path}")
    
    def load_raw_data(self):
        """加载原始数据"""
        logger.info("加载原始数据...")
        
        # 查找所有支持的图像文件
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        image_files = []
        
        for ext in image_extensions:
            image_files.extend(list(self.input_path.glob(f"**/*{ext}")))
            image_files.extend(list(self.input_path.glob(f"**/*{ext.upper()}")))
        
        logger.info(f"找到 {len(image_files)} 个图像文件")
        
        # 查找JSON数据文件
        json_files = list(self.input_path.glob("**/*.json"))
        logger.info(f"找到 {len(json_files)} 个JSON文件")
        
        return image_files, json_files
    
    def process_images(self, image_files):
        """处理图像文件"""
        logger.info("处理图像文件...")
        
        processed_images = []
        
        for i, image_file in enumerate(image_files):
            try:
                # 加载图像
                image = cv2.imread(str(image_file))
                if image is None:
                    logger.warning(f"无法加载图像: {image_file}")
                    continue
                
                # 预处理图像
                processed_image = self.preprocess_image(image)
                
                # 分类图像（轮廓图 vs 平面图）
                image_type = self.classify_image(image_file.name, processed_image)
                
                processed_images.append({
                    'original_path': image_file,
                    'processed_image': processed_image,
                    'type': image_type,
                    'filename': image_file.stem
                })
                
                if (i + 1) % 100 == 0:
                    logger.info(f"已处理 {i + 1}/{len(image_files)} 个图像")
                    
            except Exception as e:
                logger.error(f"处理图像 {image_file} 时出错: {e}")
        
        logger.info(f"成功处理 {len(processed_images)} 个图像")
        return processed_images
    
    def preprocess_image(self, image):
        """预处理单个图像"""
        # 转换为RGB
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整大小
        image = cv2.resize(image, (self.image_size, self.image_size))
        
        # 标准化
        image = image.astype(np.float32) / 255.0
        
        # 去噪
        image = cv2.bilateralFilter(image, 9, 75, 75)
        
        return image
    
    def classify_image(self, filename, image):
        """分类图像类型"""
        filename_lower = filename.lower()
        
        # 基于文件名的简单分类
        contour_keywords = ['contour', 'outline', 'boundary', '轮廓', '边界']
        floorplan_keywords = ['plan', 'layout', 'floorplan', '平面图', '布局']
        
        for keyword in contour_keywords:
            if keyword in filename_lower:
                return 'contour'
        
        for keyword in floorplan_keywords:
            if keyword in filename_lower:
                return 'floorplan'
        
        # 基于图像内容的分类（简化版）
        # 计算边缘密度
        gray = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
        edges = cv2.Canny(gray, 50, 150)
        edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
        
        # 轮廓图通常边缘密度较低，平面图边缘密度较高
        if edge_density < 0.1:
            return 'contour'
        else:
            return 'floorplan'
    
    def augment_data(self, processed_images, augmentation_factor=3):
        """数据增强"""
        logger.info(f"进行数据增强，增强因子: {augmentation_factor}")
        
        augmented_images = []
        
        for image_data in processed_images:
            image = image_data['processed_image']
            
            # 原始图像
            augmented_images.append(image_data)
            
            # 生成增强版本
            for i in range(augmentation_factor):
                augmented_image = self.apply_augmentation(image)
                
                augmented_data = image_data.copy()
                augmented_data['processed_image'] = augmented_image
                augmented_data['filename'] = f"{image_data['filename']}_aug_{i}"
                
                augmented_images.append(augmented_data)
        
        logger.info(f"数据增强完成，总计 {len(augmented_images)} 个样本")
        return augmented_images
    
    def apply_augmentation(self, image):
        """应用数据增强变换"""
        # 随机旋转
        angle = np.random.uniform(-15, 15)
        center = (self.image_size // 2, self.image_size // 2)
        rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
        image = cv2.warpAffine(image, rotation_matrix, (self.image_size, self.image_size))
        
        # 随机缩放
        scale = np.random.uniform(0.9, 1.1)
        new_size = int(self.image_size * scale)
        image = cv2.resize(image, (new_size, new_size))
        
        # 裁剪或填充到目标尺寸
        if new_size > self.image_size:
            # 中心裁剪
            start = (new_size - self.image_size) // 2
            image = image[start:start+self.image_size, start:start+self.image_size]
        elif new_size < self.image_size:
            # 填充
            pad = (self.image_size - new_size) // 2
            image = cv2.copyMakeBorder(image, pad, pad, pad, pad, cv2.BORDER_CONSTANT, value=0)
        
        # 随机水平翻转
        if np.random.random() > 0.5:
            image = cv2.flip(image, 1)
        
        # 添加噪声
        noise = np.random.normal(0, 0.02, image.shape)
        image = np.clip(image + noise, 0, 1)
        
        return image
    
    def split_dataset(self, processed_images, train_ratio=0.7, val_ratio=0.2, test_ratio=0.1):
        """划分数据集"""
        logger.info(f"划分数据集 - 训练:{train_ratio}, 验证:{val_ratio}, 测试:{test_ratio}")
        
        # 按类型分组
        contour_images = [img for img in processed_images if img['type'] == 'contour']
        floorplan_images = [img for img in processed_images if img['type'] == 'floorplan']
        
        logger.info(f"轮廓图: {len(contour_images)}, 平面图: {len(floorplan_images)}")
        
        # 分别划分每种类型
        def split_by_type(images):
            train_val, test = train_test_split(images, test_size=test_ratio, random_state=42)
            train, val = train_test_split(train_val, test_size=val_ratio/(train_ratio+val_ratio), random_state=42)
            return train, val, test
        
        contour_train, contour_val, contour_test = split_by_type(contour_images)
        floorplan_train, floorplan_val, floorplan_test = split_by_type(floorplan_images)
        
        # 合并
        train_set = contour_train + floorplan_train
        val_set = contour_val + floorplan_val
        test_set = contour_test + floorplan_test
        
        logger.info(f"数据集划分完成:")
        logger.info(f"  训练集: {len(train_set)} 样本")
        logger.info(f"  验证集: {len(val_set)} 样本")
        logger.info(f"  测试集: {len(test_set)} 样本")
        
        return train_set, val_set, test_set
    
    def save_dataset(self, train_set, val_set, test_set):
        """保存处理后的数据集"""
        logger.info("保存处理后的数据集...")
        
        def save_split(dataset, split_name):
            for i, image_data in enumerate(dataset):
                # 保存图像
                image = (image_data['processed_image'] * 255).astype(np.uint8)
                image_path = self.output_path / split_name / "images" / f"{image_data['filename']}.png"
                cv2.imwrite(str(image_path), cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
                
                # 保存元数据
                metadata = {
                    'filename': image_data['filename'],
                    'type': image_data['type'],
                    'original_path': str(image_data['original_path']),
                    'processed_at': datetime.now().isoformat()
                }
                
                metadata_path = self.output_path / split_name / f"{image_data['type']}s" / f"{image_data['filename']}.json"
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
        
        save_split(train_set, "train")
        save_split(val_set, "val")
        save_split(test_set, "test")
        
        logger.info("数据集保存完成")
    
    def generate_statistics(self, train_set, val_set, test_set):
        """生成数据统计信息"""
        logger.info("生成数据统计信息...")
        
        def analyze_split(dataset, split_name):
            contour_count = len([img for img in dataset if img['type'] == 'contour'])
            floorplan_count = len([img for img in dataset if img['type'] == 'floorplan'])
            
            return {
                'split_name': split_name,
                'total_samples': len(dataset),
                'contour_samples': contour_count,
                'floorplan_samples': floorplan_count,
                'contour_ratio': contour_count / len(dataset) if dataset else 0,
                'floorplan_ratio': floorplan_count / len(dataset) if dataset else 0
            }
        
        statistics = {
            'preprocessing_info': {
                'image_size': self.image_size,
                'processed_at': datetime.now().isoformat(),
                'input_path': str(self.input_path),
                'output_path': str(self.output_path)
            },
            'dataset_splits': [
                analyze_split(train_set, 'train'),
                analyze_split(val_set, 'val'),
                analyze_split(test_set, 'test')
            ]
        }
        
        # 保存统计信息
        stats_path = self.output_path / "statistics" / "preprocessing_stats.json"
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(statistics, f, ensure_ascii=False, indent=2)
        
        # 生成可视化图表
        self.create_visualizations(statistics)
        
        logger.info(f"统计信息已保存: {stats_path}")
    
    def create_visualizations(self, statistics):
        """创建数据可视化图表"""
        # 数据集分布图
        splits = statistics['dataset_splits']
        split_names = [s['split_name'] for s in splits]
        contour_counts = [s['contour_samples'] for s in splits]
        floorplan_counts = [s['floorplan_samples'] for s in splits]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 样本数量分布
        x = np.arange(len(split_names))
        width = 0.35
        
        ax1.bar(x - width/2, contour_counts, width, label='轮廓图', alpha=0.8)
        ax1.bar(x + width/2, floorplan_counts, width, label='平面图', alpha=0.8)
        ax1.set_xlabel('数据集')
        ax1.set_ylabel('样本数量')
        ax1.set_title('数据集样本分布')
        ax1.set_xticks(x)
        ax1.set_xticklabels(split_names)
        ax1.legend()
        
        # 比例饼图
        total_contour = sum(contour_counts)
        total_floorplan = sum(floorplan_counts)
        
        ax2.pie([total_contour, total_floorplan], 
                labels=['轮廓图', '平面图'], 
                autopct='%1.1f%%',
                startangle=90)
        ax2.set_title('数据类型分布')
        
        plt.tight_layout()
        plt.savefig(self.output_path / "statistics" / "dataset_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info("可视化图表已生成")

def main():
    parser = argparse.ArgumentParser(description="预处理建筑数据")
    parser.add_argument("--input_path", type=str, required=True, help="原始数据路径")
    parser.add_argument("--output_path", type=str, required=True, help="处理后数据输出路径")
    parser.add_argument("--image_size", type=int, default=512, help="图像尺寸")
    parser.add_argument("--augmentation_factor", type=int, default=3, help="数据增强因子")
    parser.add_argument("--train_ratio", type=float, default=0.7, help="训练集比例")
    parser.add_argument("--val_ratio", type=float, default=0.2, help="验证集比例")
    parser.add_argument("--test_ratio", type=float, default=0.1, help="测试集比例")
    
    args = parser.parse_args()
    
    # 验证参数
    if abs(args.train_ratio + args.val_ratio + args.test_ratio - 1.0) > 1e-6:
        raise ValueError("训练、验证、测试集比例之和必须等于1")
    
    # 创建预处理器
    preprocessor = DataPreprocessor(args.input_path, args.output_path, args.image_size)
    
    # 执行预处理流程
    logger.info("开始数据预处理...")
    
    # 1. 加载原始数据
    image_files, json_files = preprocessor.load_raw_data()
    
    # 2. 处理图像
    processed_images = preprocessor.process_images(image_files)
    
    # 3. 数据增强
    if args.augmentation_factor > 0:
        processed_images = preprocessor.augment_data(processed_images, args.augmentation_factor)
    
    # 4. 划分数据集
    train_set, val_set, test_set = preprocessor.split_dataset(
        processed_images, args.train_ratio, args.val_ratio, args.test_ratio
    )
    
    # 5. 保存数据集
    preprocessor.save_dataset(train_set, val_set, test_set)
    
    # 6. 生成统计信息
    preprocessor.generate_statistics(train_set, val_set, test_set)
    
    logger.info("数据预处理完成!")

if __name__ == "__main__":
    main()
