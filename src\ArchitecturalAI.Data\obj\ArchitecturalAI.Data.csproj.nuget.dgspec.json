{"format": 1, "restore": {"D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Data\\ArchitecturalAI.Data.csproj": {}}, "projects": {"D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\ArchitecturalAI.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\ArchitecturalAI.Core.csproj", "projectName": "ArchitecturalAI.Core", "projectPath": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\ArchitecturalAI.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}, "System.Numerics.Vectors": {"target": "Package", "version": "[4.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Data\\ArchitecturalAI.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Data\\ArchitecturalAI.Data.csproj", "projectName": "ArchitecturalAI.Data", "projectPath": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Data\\ArchitecturalAI.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\ArchitecturalAI.Core.csproj": {"projectPath": "D:\\MyWork\\12AIAgent\\02Augment\\01Code\\02AI训练\\src\\ArchitecturalAI.Core\\ArchitecturalAI.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CsvHelper": {"target": "Package", "version": "[30.0.1, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SkiaSharp": {"target": "Package", "version": "[2.88.8, )"}, "System.Drawing.Common": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}