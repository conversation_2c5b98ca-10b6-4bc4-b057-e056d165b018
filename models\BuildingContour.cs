using System.Drawing;

namespace ArchitecturalAI.Models;

/// <summary>
/// 表示建筑楼层的外轮廓
/// </summary>
public class BuildingContour
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public List<Point2D> Points { get; set; } = new();
    public float FloorHeight { get; set; } = 3.0f;
    public int FloorNumber { get; set; } = 1;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 计算轮廓的面积
    /// </summary>
    public float CalculateArea()
    {
        if (Points.Count < 3) return 0;

        float area = 0;
        for (int i = 0; i < Points.Count; i++)
        {
            var current = Points[i];
            var next = Points[(i + 1) % Points.Count];
            area += current.X * next.Y - next.X * current.Y;
        }
        return MathF.Abs(area) / 2.0f;
    }

    /// <summary>
    /// 计算轮廓的周长
    /// </summary>
    public float CalculatePerimeter()
    {
        if (Points.Count < 2) return 0;

        float perimeter = 0;
        for (int i = 0; i < Points.Count; i++)
        {
            var current = Points[i];
            var next = Points[(i + 1) % Points.Count];
            perimeter += current.DistanceTo(next);
        }
        return perimeter;
    }

    /// <summary>
    /// 获取轮廓的边界框
    /// </summary>
    public RectangleF GetBoundingBox()
    {
        if (Points.Count == 0) return RectangleF.Empty;

        var minX = Points.Min(p => p.X);
        var maxX = Points.Max(p => p.X);
        var minY = Points.Min(p => p.Y);
        var maxY = Points.Max(p => p.Y);

        return new RectangleF(minX, minY, maxX - minX, maxY - minY);
    }

    /// <summary>
    /// 检查轮廓是否闭合
    /// </summary>
    public bool IsClosed()
    {
        if (Points.Count < 3) return false;
        return Points[0].DistanceTo(Points[^1]) < 0.01f;
    }
}
