using ArchitecturalAI.Models;
using ArchitecturalAI.Services;
using Microsoft.Win32;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using Newtonsoft.Json;

namespace ArchitecturalAI;

public partial class MainWindow : Window
{
    private readonly IFloorPlanGenerator? _floorPlanGenerator;
    private BuildingContour _currentContour;
    private List<Point> _contourPoints;
    private bool _isDrawing;
    private Point _lastPoint;
    private Polyline? _currentPolyline;

    public MainWindow()
    {
        InitializeComponent();
        InitializeDrawing();
    }

    public MainWindow(IFloorPlanGenerator floorPlanGenerator) : this()
    {
        _floorPlanGenerator = floorPlanGenerator;
    }

    private void InitializeDrawing()
    {
        _currentContour = new BuildingContour();
        _contourPoints = new List<Point>();
        _isDrawing = false;
        
        // 设置默认工具
        SelectTool.IsChecked = true;
        
        UpdateStatusText("就绪 - 选择绘制工具开始绘制轮廓");
    }

    #region 菜单事件处理

    private void NewProject_Click(object sender, RoutedEventArgs e)
    {
        if (MessageBox.Show("确定要新建项目吗？当前工作将丢失。", "新建项目", 
            MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
        {
            ClearCanvas();
            InitializeDrawing();
        }
    }

    private void OpenProject_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new OpenFileDialog
        {
            Filter = "项目文件 (*.json)|*.json|所有文件 (*.*)|*.*",
            Title = "打开项目"
        };

        if (dialog.ShowDialog() == true)
        {
            // TODO: 实现项目加载
            UpdateStatusText($"加载项目: {dialog.FileName}");
        }
    }

    private void SaveProject_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new SaveFileDialog
        {
            Filter = "项目文件 (*.json)|*.json|所有文件 (*.*)|*.*",
            Title = "保存项目"
        };

        if (dialog.ShowDialog() == true)
        {
            // TODO: 实现项目保存
            UpdateStatusText($"保存项目: {dialog.FileName}");
        }
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void Undo_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现撤销功能
        UpdateStatusText("撤销操作");
    }

    private void Redo_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现重做功能
        UpdateStatusText("重做操作");
    }

    private void ClearContour_Click(object sender, RoutedEventArgs e)
    {
        ClearCanvas();
        InitializeDrawing();
    }

    private void About_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("建筑平面图AI生成系统 v1.0\n\n基于深度学习技术的智能建筑设计工具", 
            "关于", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    #endregion

    #region 绘图事件处理

    private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (DrawTool.IsChecked == true)
        {
            var position = e.GetPosition(DrawingCanvas);
            
            if (!_isDrawing)
            {
                // 开始绘制新的轮廓
                StartNewContour(position);
            }
            else
            {
                // 添加点到当前轮廓
                AddPointToContour(position);
            }
        }
    }

    private void Canvas_MouseMove(object sender, MouseEventArgs e)
    {
        var position = e.GetPosition(DrawingCanvas);
        CoordinateText.Text = $"坐标: ({position.X:F0}, {position.Y:F0})";

        if (_isDrawing && _currentPolyline != null)
        {
            // 更新临时线条
            UpdateTemporaryLine(position);
        }
    }

    private void Canvas_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        // 处理鼠标释放事件
    }

    private void Canvas_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (_isDrawing)
        {
            // 右键完成轮廓绘制
            FinishContour();
        }
    }

    #endregion

    #region 绘图辅助方法

    private void StartNewContour(Point startPoint)
    {
        _isDrawing = true;
        _contourPoints.Clear();
        _contourPoints.Add(startPoint);
        _lastPoint = startPoint;

        // 创建新的折线
        _currentPolyline = new Polyline
        {
            Stroke = Brushes.Blue,
            StrokeThickness = 2,
            Points = new PointCollection { startPoint }
        };

        DrawingCanvas.Children.Add(_currentPolyline);
        UpdateStatusText("绘制中 - 左键添加点，右键完成");
    }

    private void AddPointToContour(Point point)
    {
        _contourPoints.Add(point);
        _currentPolyline?.Points.Add(point);
        _lastPoint = point;
    }

    private void UpdateTemporaryLine(Point currentPoint)
    {
        // 这里可以添加临时线条显示逻辑
    }

    private void FinishContour()
    {
        if (_contourPoints.Count >= 3)
        {
            // 闭合轮廓
            _currentPolyline?.Points.Add(_contourPoints[0]);
            
            // 更新轮廓数据
            UpdateContourData();
            
            _isDrawing = false;
            UpdateStatusText($"轮廓绘制完成 - 共 {_contourPoints.Count} 个点");
        }
        else
        {
            MessageBox.Show("轮廓至少需要3个点", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void UpdateContourData()
    {
        _currentContour.Points.Clear();
        foreach (var point in _contourPoints)
        {
            _currentContour.Points.Add(new Point2D((float)point.X, (float)point.Y));
        }

        _currentContour.Name = ContourName.Text;
        if (float.TryParse(FloorHeight.Text, out float height))
            _currentContour.FloorHeight = height;
        if (int.TryParse(FloorNumber.Text, out int floor))
            _currentContour.FloorNumber = floor;

        // 更新统计信息
        UpdateStatistics();
    }

    private void ClearCanvas()
    {
        DrawingCanvas.Children.Clear();
        _contourPoints.Clear();
        _isDrawing = false;
        _currentPolyline = null;
    }

    #endregion

    #region 工具栏事件

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现放大功能
        UpdateStatusText("放大视图");
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现缩小功能
        UpdateStatusText("缩小视图");
    }

    private void ZoomFit_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现适应窗口功能
        UpdateStatusText("适应窗口");
    }

    private void GridToggle_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 切换网格显示
        UpdateStatusText("切换网格显示");
    }

    private void SnapToggle_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 切换网格对齐
        UpdateStatusText("切换网格对齐");
    }

    #endregion

    #region 属性和生成

    private void ApplyProperties_Click(object sender, RoutedEventArgs e)
    {
        UpdateContourData();
        UpdateStatusText("属性已应用");
    }

    private async void GenerateFloorPlan_Click(object sender, RoutedEventArgs e)
    {
        if (_currentContour.Points.Count < 3)
        {
            MessageBox.Show("请先绘制建筑轮廓", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (_floorPlanGenerator == null)
        {
            MessageBox.Show("AI模型未加载", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        try
        {
            UpdateStatusText("正在生成平面图...");
            ProgressBar.Visibility = Visibility.Visible;

            var parameters = CreateGenerationParameters();
            var floorPlan = await _floorPlanGenerator.GenerateAsync(_currentContour, parameters);

            // 显示生成结果
            DisplayFloorPlan(floorPlan);
            UpdateStatusText("平面图生成完成");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"生成失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatusText("生成失败");
        }
        finally
        {
            ProgressBar.Visibility = Visibility.Collapsed;
        }
    }

    private async void BatchGenerate_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现批量生成
        UpdateStatusText("批量生成功能开发中...");
    }

    #endregion

    #region 辅助方法

    private GenerationParameters CreateGenerationParameters()
    {
        return new GenerationParameters
        {
            RoomCount = (int)RoomCount.Value,
            IncludeBalcony = IncludeBalcony.IsChecked == true,
            IncludeStudy = IncludeStudy.IsChecked == true,
            Style = ((ComboBoxItem)DesignStyle.SelectedItem)?.Content?.ToString() ?? "现代简约"
        };
    }

    private void DisplayFloorPlan(FloorPlan floorPlan)
    {
        // TODO: 在预览区域显示生成的平面图
        UpdateStatistics(floorPlan);
    }

    private void UpdateStatistics(FloorPlan? floorPlan = null)
    {
        if (floorPlan != null)
        {
            AreaInfo.Text = $"总面积: {floorPlan.CalculateTotalArea():F1} 平方米";
            RoomInfo.Text = $"房间数量: {floorPlan.Rooms.Count}";
            ConfidenceInfo.Text = $"置信度: {floorPlan.ConfidenceScore * 100:F1}%";
        }
        else if (_currentContour.Points.Count > 0)
        {
            AreaInfo.Text = $"轮廓面积: {_currentContour.CalculateArea():F1} 平方米";
            RoomInfo.Text = "房间数量: --";
            ConfidenceInfo.Text = "置信度: --%";
        }
    }

    private void UpdateStatusText(string text)
    {
        StatusText.Text = text;
    }

    #endregion

    #region 结果操作

    private void SaveResult_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 保存生成结果
        UpdateStatusText("保存结果功能开发中...");
    }

    private void ExportImage_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 导出为图片
        UpdateStatusText("导出图片功能开发中...");
    }

    private void ExportDWG_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 导出为DWG
        UpdateStatusText("导出DWG功能开发中...");
    }

    #endregion
}
