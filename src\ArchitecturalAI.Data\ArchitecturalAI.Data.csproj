<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="SkiaSharp" Version="2.88.8" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="CsvHelper" Version="30.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ArchitecturalAI.Core\ArchitecturalAI.Core.csproj" />
  </ItemGroup>

</Project>
