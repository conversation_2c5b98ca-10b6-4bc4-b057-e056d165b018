using System.Numerics;

namespace ArchitecturalAI.Core.Models;

/// <summary>
/// 表示二维空间中的点
/// </summary>
public struct Point2D : IEquatable<Point2D>
{
    public float X { get; set; }
    public float Y { get; set; }

    public Point2D(float x, float y)
    {
        X = x;
        Y = y;
    }

    public Point2D(Vector2 vector)
    {
        X = vector.X;
        Y = vector.Y;
    }

    public Vector2 ToVector2() => new(X, Y);

    public float DistanceTo(Point2D other)
    {
        var dx = X - other.X;
        var dy = Y - other.Y;
        return MathF.Sqrt(dx * dx + dy * dy);
    }

    public Point2D Translate(float dx, float dy) => new(X + dx, Y + dy);

    public Point2D Scale(float factor) => new(X * factor, Y * factor);

    public Point2D Rotate(float angleRadians, Point2D center)
    {
        var cos = MathF.Cos(angleRadians);
        var sin = MathF.Sin(angleRadians);
        var dx = X - center.X;
        var dy = Y - center.Y;
        
        return new Point2D(
            center.X + dx * cos - dy * sin,
            center.Y + dx * sin + dy * cos
        );
    }

    public bool Equals(Point2D other) => 
        MathF.Abs(X - other.X) < float.Epsilon && 
        MathF.Abs(Y - other.Y) < float.Epsilon;

    public override bool Equals(object? obj) => obj is Point2D other && Equals(other);

    public override int GetHashCode() => HashCode.Combine(X, Y);

    public static bool operator ==(Point2D left, Point2D right) => left.Equals(right);
    public static bool operator !=(Point2D left, Point2D right) => !left.Equals(right);

    public override string ToString() => $"({X:F2}, {Y:F2})";
}
