using ArchitecturalAI.Models;
using ArchitecturalAI.Services;
using Microsoft.Win32;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using Newtonsoft.Json;

namespace ArchitecturalAI;

public partial class MainWindow : Window
{
    private readonly IFloorPlanGenerator _floorPlanGenerator;
    private BuildingContour _currentContour;
    private List<Point> _contourPoints;
    private bool _isDrawing;
    private Polyline? _currentPolyline;
    private FloorPlan? _currentFloorPlan;

    public MainWindow()
    {
        InitializeComponent();
        _floorPlanGenerator = new FloorPlanGenerator();
        InitializeDrawing();
    }

    private void InitializeDrawing()
    {
        _currentContour = new BuildingContour();
        _contourPoints = new List<Point>();
        _isDrawing = false;
        
        SelectTool.IsChecked = true;
        UpdateStatusText("就绪 - 选择绘制工具开始绘制轮廓");
    }

    #region 菜单事件处理

    private void NewProject_Click(object sender, RoutedEventArgs e)
    {
        if (MessageBox.Show("确定要新建项目吗？当前工作将丢失。", "新建项目", 
            MessageBoxButton.YesNo, MessageBoxImage.Question) == MessageBoxResult.Yes)
        {
            ClearCanvas();
            InitializeDrawing();
        }
    }

    private void SaveProject_Click(object sender, RoutedEventArgs e)
    {
        var dialog = new SaveFileDialog
        {
            Filter = "项目文件 (*.json)|*.json",
            Title = "保存项目"
        };

        if (dialog.ShowDialog() == true)
        {
            try
            {
                var projectData = new
                {
                    Contour = _currentContour,
                    FloorPlan = _currentFloorPlan,
                    SavedAt = DateTime.UtcNow
                };

                var json = JsonConvert.SerializeObject(projectData, Formatting.Indented);
                File.WriteAllText(dialog.FileName, json);
                UpdateStatusText($"项目已保存: {dialog.FileName}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void About_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("建筑平面图AI生成系统 v1.0\n\n基于深度学习技术的智能建筑设计工具", 
            "关于", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    #endregion

    #region 绘图事件处理

    private void Canvas_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (DrawTool.IsChecked == true)
        {
            var position = e.GetPosition(DrawingCanvas);
            
            if (!_isDrawing)
            {
                StartNewContour(position);
            }
            else
            {
                AddPointToContour(position);
            }
        }
    }

    private void Canvas_MouseMove(object sender, MouseEventArgs e)
    {
        var position = e.GetPosition(DrawingCanvas);
        CoordinateText.Text = $"坐标: ({position.X:F0}, {position.Y:F0})";
    }

    private void Canvas_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (_isDrawing)
        {
            FinishContour();
        }
    }

    #endregion

    #region 绘图辅助方法

    private void StartNewContour(Point startPoint)
    {
        _isDrawing = true;
        _contourPoints.Clear();
        _contourPoints.Add(startPoint);

        _currentPolyline = new Polyline
        {
            Stroke = Brushes.Blue,
            StrokeThickness = 2,
            Points = new PointCollection { startPoint }
        };

        DrawingCanvas.Children.Add(_currentPolyline);
        UpdateStatusText("绘制中 - 左键添加点，右键完成");
    }

    private void AddPointToContour(Point point)
    {
        _contourPoints.Add(point);
        _currentPolyline?.Points.Add(point);
    }

    private void FinishContour()
    {
        if (_contourPoints.Count >= 3)
        {
            _currentPolyline?.Points.Add(_contourPoints[0]); // 闭合轮廓
            UpdateContourData();
            _isDrawing = false;
            UpdateStatusText($"轮廓绘制完成 - 共 {_contourPoints.Count} 个点");
        }
        else
        {
            MessageBox.Show("轮廓至少需要3个点", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void UpdateContourData()
    {
        _currentContour.Points.Clear();
        foreach (var point in _contourPoints)
        {
            _currentContour.Points.Add(new Point2D((float)point.X, (float)point.Y));
        }

        _currentContour.Name = ContourName.Text;
        if (float.TryParse(FloorHeight.Text, out float height))
            _currentContour.FloorHeight = height;
        if (int.TryParse(FloorNumber.Text, out int floor))
            _currentContour.FloorNumber = floor;

        UpdateStatistics();
    }

    private void ClearCanvas()
    {
        DrawingCanvas.Children.Clear();
        PreviewCanvas.Children.Clear();
        _contourPoints.Clear();
        _isDrawing = false;
        _currentPolyline = null;
        _currentFloorPlan = null;
        RoomList.ItemsSource = null;
        UpdateStatistics();
    }

    #endregion

    #region 工具栏事件

    private void ClearCanvas_Click(object sender, RoutedEventArgs e)
    {
        ClearCanvas();
        InitializeDrawing();
    }

    private void ZoomFit_Click(object sender, RoutedEventArgs e)
    {
        UpdateStatusText("适应窗口");
    }

    #endregion

    #region 生成和结果处理

    private async void GenerateFloorPlan_Click(object sender, RoutedEventArgs e)
    {
        if (_currentContour.Points.Count < 3)
        {
            MessageBox.Show("请先绘制建筑轮廓", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            UpdateStatusText("正在生成平面图...");
            ProgressBar.Visibility = Visibility.Visible;

            var parameters = CreateGenerationParameters();
            _currentFloorPlan = await _floorPlanGenerator.GenerateAsync(_currentContour, parameters);

            DisplayFloorPlan(_currentFloorPlan);
            UpdateStatusText("平面图生成完成");
        }
        catch (Exception ex)
        {
            MessageBox.Show($"生成失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            UpdateStatusText("生成失败");
        }
        finally
        {
            ProgressBar.Visibility = Visibility.Collapsed;
        }
    }

    private GenerationParameters CreateGenerationParameters()
    {
        return new GenerationParameters
        {
            RoomCount = (int)RoomCount.Value,
            IncludeBalcony = IncludeBalcony.IsChecked == true,
            IncludeStudy = IncludeStudy.IsChecked == true,
            Style = ((ComboBoxItem)DesignStyle.SelectedItem)?.Content?.ToString() ?? "现代简约"
        };
    }

    private void DisplayFloorPlan(FloorPlan floorPlan)
    {
        // 清除预览画布
        PreviewCanvas.Children.Clear();

        // 绘制房间
        var colors = new[] { Brushes.LightBlue, Brushes.LightGreen, Brushes.LightYellow, Brushes.LightPink, Brushes.LightGray };
        
        for (int i = 0; i < floorPlan.Rooms.Count; i++)
        {
            var room = floorPlan.Rooms[i];
            if (room.Boundary.Count >= 3)
            {
                var polygon = new Polygon
                {
                    Fill = colors[i % colors.Length],
                    Stroke = Brushes.Black,
                    StrokeThickness = 1,
                    Points = new PointCollection(room.Boundary.Select(p => new Point(p.X * 0.2, p.Y * 0.2)))
                };
                PreviewCanvas.Children.Add(polygon);
            }
        }

        // 更新房间列表
        var roomData = floorPlan.Rooms.Select(r => new
        {
            Name = r.Name,
            Type = GetRoomTypeName(r.Type),
            Area = r.CalculateArea()
        }).ToList();

        RoomList.ItemsSource = roomData;
        UpdateStatistics(floorPlan);
    }

    private string GetRoomTypeName(RoomType type)
    {
        return type switch
        {
            RoomType.LivingRoom => "客厅",
            RoomType.Bedroom => "卧室",
            RoomType.Kitchen => "厨房",
            RoomType.Bathroom => "卫生间",
            RoomType.Balcony => "阳台",
            RoomType.Study => "书房",
            RoomType.DiningRoom => "餐厅",
            _ => "其他"
        };
    }

    #endregion

    #region 导出功能

    private void SaveResult_Click(object sender, RoutedEventArgs e)
    {
        if (_currentFloorPlan == null)
        {
            MessageBox.Show("没有可保存的结果", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        var dialog = new SaveFileDialog
        {
            Filter = "JSON文件 (*.json)|*.json",
            Title = "保存结果"
        };

        if (dialog.ShowDialog() == true)
        {
            try
            {
                var json = JsonConvert.SerializeObject(_currentFloorPlan, Formatting.Indented);
                File.WriteAllText(dialog.FileName, json);
                UpdateStatusText($"结果已保存: {dialog.FileName}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void ExportImage_Click(object sender, RoutedEventArgs e)
    {
        UpdateStatusText("导出图片功能开发中...");
    }

    #endregion

    #region 辅助方法

    private void UpdateStatistics(FloorPlan? floorPlan = null)
    {
        if (floorPlan != null)
        {
            AreaInfo.Text = $"总面积: {floorPlan.CalculateTotalArea():F1} 平方米";
            RoomInfo.Text = $"房间数量: {floorPlan.Rooms.Count}";
            ConfidenceInfo.Text = $"置信度: {floorPlan.ConfidenceScore * 100:F1}%";
        }
        else if (_currentContour.Points.Count > 0)
        {
            AreaInfo.Text = $"轮廓面积: {_currentContour.CalculateArea():F1} 平方米";
            RoomInfo.Text = "房间数量: --";
            ConfidenceInfo.Text = "置信度: --%";
        }
        else
        {
            AreaInfo.Text = "总面积: -- 平方米";
            RoomInfo.Text = "房间数量: --";
            ConfidenceInfo.Text = "置信度: --%";
        }
    }

    private void UpdateStatusText(string text)
    {
        StatusText.Text = text;
    }

    #endregion
}
