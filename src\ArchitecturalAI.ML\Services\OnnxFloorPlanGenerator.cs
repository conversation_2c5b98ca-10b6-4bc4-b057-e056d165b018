using ArchitecturalAI.Core.Models;
using ArchitecturalAI.ML.Interfaces;
using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Drawing.Processing;

namespace ArchitecturalAI.ML.Services;

/// <summary>
/// 基于ONNX Runtime的楼层平面图生成器
/// </summary>
public class OnnxFloorPlanGenerator : IFloorPlanGenerator, IDisposable
{
    private InferenceSession? _session;
    private ModelInfo? _modelInfo;
    private readonly SessionOptions _sessionOptions;
    private bool _disposed = false;

    public bool IsModelLoaded => _session != null;

    public OnnxFloorPlanGenerator()
    {
        _sessionOptions = new SessionOptions();
        
        // 尝试使用GPU加速
        try
        {
            _sessionOptions.AppendExecutionProvider_CUDA();
        }
        catch
        {
            // GPU不可用时回退到CPU
            _sessionOptions.AppendExecutionProvider_CPU();
        }
    }

    public async Task LoadModelAsync(string modelPath)
    {
        if (!File.Exists(modelPath))
            throw new FileNotFoundException($"模型文件不存在: {modelPath}");

        try
        {
            _session?.Dispose();
            _session = new InferenceSession(modelPath, _sessionOptions);

            // 从模型元数据中提取信息
            _modelInfo = ExtractModelInfo(_session);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"加载模型失败: {ex.Message}", ex);
        }
    }

    public void UnloadModel()
    {
        _session?.Dispose();
        _session = null;
        _modelInfo = null;
    }

    public ModelInfo? GetModelInfo() => _modelInfo;

    public async Task<FloorPlan> GenerateAsync(
        BuildingContour contour, 
        GenerationParameters parameters, 
        CancellationToken cancellationToken = default)
    {
        if (!IsModelLoaded)
            throw new InvalidOperationException("模型未加载");

        try
        {
            // 1. 预处理输入数据
            var inputTensor = await PreprocessContourAsync(contour, parameters);

            // 2. 运行推理
            var outputs = await RunInferenceAsync(inputTensor, cancellationToken);

            // 3. 后处理输出
            var floorPlan = await PostprocessOutputAsync(outputs, contour, parameters);

            return floorPlan;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"生成楼层平面图失败: {ex.Message}", ex);
        }
    }

    public async Task<List<FloorPlan>> GenerateMultipleAsync(
        BuildingContour contour, 
        GenerationParameters parameters, 
        int count = 3, 
        CancellationToken cancellationToken = default)
    {
        var results = new List<FloorPlan>();

        for (int i = 0; i < count; i++)
        {
            cancellationToken.ThrowIfCancellationRequested();
            
            // 为每次生成添加随机种子变化
            var modifiedParams = CloneParameters(parameters);
            modifiedParams.CustomParameters["seed"] = Random.Shared.Next();
            
            var floorPlan = await GenerateAsync(contour, modifiedParams, cancellationToken);
            results.Add(floorPlan);
        }

        return results;
    }

    private async Task<Tensor<float>> PreprocessContourAsync(
        BuildingContour contour, 
        GenerationParameters parameters)
    {
        var inputSize = _modelInfo?.InputWidth ?? 512;
        
        // 创建轮廓图像
        using var image = new Image<Rgb24>(inputSize, inputSize);
        
        // 绘制轮廓
        await DrawContourOnImage(image, contour);
        
        // 转换为张量
        var tensor = ImageToTensor(image);
        
        return tensor;
    }

    private async Task<IDisposableReadOnlyCollection<DisposableNamedOnnxValue>> RunInferenceAsync(
        Tensor<float> inputTensor, 
        CancellationToken cancellationToken)
    {
        var inputName = _session!.InputMetadata.Keys.First();
        var inputs = new List<NamedOnnxValue>
        {
            NamedOnnxValue.CreateFromTensor(inputName, inputTensor)
        };

        return await Task.Run(() => _session.Run(inputs), cancellationToken);
    }

    private async Task<FloorPlan> PostprocessOutputAsync(
        IDisposableReadOnlyCollection<DisposableNamedOnnxValue> outputs,
        BuildingContour contour,
        GenerationParameters parameters)
    {
        var outputTensor = outputs.First().AsTensor<float>();
        
        // 将输出张量转换为图像
        using var outputImage = TensorToImage(outputTensor);
        
        // 从图像中提取房间、墙体等信息
        var floorPlan = await ExtractFloorPlanFromImage(outputImage, contour, parameters);
        
        return floorPlan;
    }

    private async Task DrawContourOnImage(Image<Rgb24> image, BuildingContour contour)
    {
        if (contour.Points.Count < 2) return;

        var bounds = contour.GetBoundingBox();
        var scale = Math.Min(image.Width / bounds.Width, image.Height / bounds.Height) * 0.8f;
        var offsetX = (image.Width - bounds.Width * scale) / 2;
        var offsetY = (image.Height - bounds.Height * scale) / 2;

        image.Mutate(ctx =>
        {
            // 清空背景
            ctx.BackgroundColor(Color.Black);

            // 绘制轮廓线
            var points = contour.Points.Select(p => new PointF(
                (p.X - bounds.X) * scale + offsetX,
                (p.Y - bounds.Y) * scale + offsetY
            )).ToArray();

            // 这里需要使用ImageSharp的绘图功能
            // 简化实现，实际需要更复杂的绘图逻辑
            if (points.Length > 2)
            {
                ctx.DrawPolygon(Color.White, 2.0f, points);
            }
        });

        await Task.CompletedTask;
    }

    private Tensor<float> ImageToTensor(Image<Rgb24> image)
    {
        var tensor = new DenseTensor<float>(new[] { 1, 3, image.Height, image.Width });
        
        image.ProcessPixelRows(accessor =>
        {
            for (int y = 0; y < accessor.Height; y++)
            {
                var pixelRow = accessor.GetRowSpan(y);
                for (int x = 0; x < pixelRow.Length; x++)
                {
                    var pixel = pixelRow[x];
                    tensor[0, 0, y, x] = pixel.R / 255.0f;
                    tensor[0, 1, y, x] = pixel.G / 255.0f;
                    tensor[0, 2, y, x] = pixel.B / 255.0f;
                }
            }
        });

        return tensor;
    }

    private Image<Rgb24> TensorToImage(Tensor<float> tensor)
    {
        var height = tensor.Dimensions[2];
        var width = tensor.Dimensions[3];
        var image = new Image<Rgb24>(width, height);

        image.ProcessPixelRows(accessor =>
        {
            for (int y = 0; y < accessor.Height; y++)
            {
                var pixelRow = accessor.GetRowSpan(y);
                for (int x = 0; x < pixelRow.Length; x++)
                {
                    var r = (byte)(Math.Clamp(tensor[0, 0, y, x], 0, 1) * 255);
                    var g = (byte)(Math.Clamp(tensor[0, 1, y, x], 0, 1) * 255);
                    var b = (byte)(Math.Clamp(tensor[0, 2, y, x], 0, 1) * 255);
                    pixelRow[x] = new Rgb24(r, g, b);
                }
            }
        });

        return image;
    }

    private async Task<FloorPlan> ExtractFloorPlanFromImage(
        Image<Rgb24> image, 
        BuildingContour contour, 
        GenerationParameters parameters)
    {
        // 这里需要实现图像分析算法来提取房间、墙体等信息
        // 简化实现，实际需要更复杂的计算机视觉算法
        
        var floorPlan = new FloorPlan
        {
            ContourId = contour.Id,
            Name = $"Generated Plan for {contour.Name}",
            SourceContour = contour,
            Parameters = parameters,
            ConfidenceScore = 0.85f // 模拟置信度
        };

        // 模拟生成一些房间
        await GenerateSampleRooms(floorPlan, contour);

        return floorPlan;
    }

    private async Task GenerateSampleRooms(FloorPlan floorPlan, BuildingContour contour)
    {
        var bounds = contour.GetBoundingBox();
        
        // 简化的房间生成逻辑
        floorPlan.Rooms.Add(new Room
        {
            Name = "客厅",
            Type = RoomType.LivingRoom,
            Boundary = new List<Point2D>
            {
                new(bounds.X, bounds.Y),
                new(bounds.X + bounds.Width * 0.6f, bounds.Y),
                new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.7f),
                new(bounds.X, bounds.Y + bounds.Height * 0.7f)
            }
        });

        floorPlan.Rooms.Add(new Room
        {
            Name = "卧室",
            Type = RoomType.Bedroom,
            Boundary = new List<Point2D>
            {
                new(bounds.X + bounds.Width * 0.6f, bounds.Y),
                new(bounds.X + bounds.Width, bounds.Y),
                new(bounds.X + bounds.Width, bounds.Y + bounds.Height * 0.5f),
                new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.5f)
            }
        });

        await Task.CompletedTask;
    }

    private ModelInfo ExtractModelInfo(InferenceSession session)
    {
        var metadata = session.ModelMetadata;
        return new ModelInfo
        {
            Name = metadata.CustomMetadataMap.GetValueOrDefault("name", "Unknown"),
            Version = metadata.Version.ToString(),
            Description = metadata.Description,
            Type = ModelType.ConditionalGAN,
            InputWidth = 512,
            InputHeight = 512,
            OutputWidth = 512,
            OutputHeight = 512
        };
    }

    private GenerationParameters CloneParameters(GenerationParameters original)
    {
        return new GenerationParameters
        {
            RoomCount = original.RoomCount,
            MinRoomArea = original.MinRoomArea,
            IncludeBalcony = original.IncludeBalcony,
            IncludeStudy = original.IncludeStudy,
            Style = original.Style,
            CustomParameters = new Dictionary<string, object>(original.CustomParameters)
        };
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _session?.Dispose();
            _sessionOptions?.Dispose();
            _disposed = true;
        }
    }
}
