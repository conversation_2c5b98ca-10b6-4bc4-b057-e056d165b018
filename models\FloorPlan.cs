namespace ArchitecturalAI.Models;

/// <summary>
/// 表示生成的楼层平面图
/// </summary>
public class FloorPlan
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ContourId { get; set; }
    public string Name { get; set; } = string.Empty;
    public List<Room> Rooms { get; set; } = new();
    public BuildingContour? SourceContour { get; set; }
    public GenerationParameters? Parameters { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public float ConfidenceScore { get; set; } = 0.0f;

    /// <summary>
    /// 计算总建筑面积
    /// </summary>
    public float CalculateTotalArea()
    {
        return Rooms.Sum(room => room.CalculateArea());
    }

    /// <summary>
    /// 获取房间类型统计
    /// </summary>
    public Dictionary<RoomType, int> GetRoomTypeStatistics()
    {
        return Rooms.GroupBy(r => r.Type)
                   .ToDictionary(g => g.Key, g => g.Count());
    }
}

/// <summary>
/// 表示房间
/// </summary>
public class Room
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public RoomType Type { get; set; }
    public List<Point2D> Boundary { get; set; } = new();

    public float CalculateArea()
    {
        if (Boundary.Count < 3) return 0;

        float area = 0;
        for (int i = 0; i < Boundary.Count; i++)
        {
            var current = Boundary[i];
            var next = Boundary[(i + 1) % Boundary.Count];
            area += current.X * next.Y - next.X * current.Y;
        }
        return MathF.Abs(area) / 2.0f;
    }
}

/// <summary>
/// 房间类型枚举
/// </summary>
public enum RoomType
{
    LivingRoom,     // 客厅
    Bedroom,        // 卧室
    Kitchen,        // 厨房
    Bathroom,       // 卫生间
    Balcony,        // 阳台
    Study,          // 书房
    DiningRoom,     // 餐厅
    Storage,        // 储藏室
    Corridor,       // 走廊
    Entrance,       // 玄关
    Other           // 其他
}

/// <summary>
/// 生成参数
/// </summary>
public class GenerationParameters
{
    public int RoomCount { get; set; } = 3;
    public float MinRoomArea { get; set; } = 8.0f;
    public bool IncludeBalcony { get; set; } = true;
    public bool IncludeStudy { get; set; } = false;
    public string Style { get; set; } = "Modern";
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}
