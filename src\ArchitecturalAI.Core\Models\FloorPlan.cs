using System.Drawing;

namespace ArchitecturalAI.Core.Models;

/// <summary>
/// 表示生成的楼层平面图
/// </summary>
public class FloorPlan
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Guid ContourId { get; set; }
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Room> Rooms { get; set; } = new();
    public List<Wall> Walls { get; set; } = new();
    public List<Door> Doors { get; set; } = new();
    public List<Window> Windows { get; set; } = new();
    public BuildingContour? SourceContour { get; set; }
    public GenerationParameters? Parameters { get; set; }
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public float ConfidenceScore { get; set; } = 0.0f;

    /// <summary>
    /// 计算总建筑面积
    /// </summary>
    public float CalculateTotalArea()
    {
        return Rooms.Sum(room => room.CalculateArea());
    }

    /// <summary>
    /// 获取房间类型统计
    /// </summary>
    public Dictionary<RoomType, int> GetRoomTypeStatistics()
    {
        return Rooms.GroupBy(r => r.Type)
                   .ToDictionary(g => g.Key, g => g.Count());
    }

    /// <summary>
    /// 验证平面图的合理性
    /// </summary>
    public ValidationResult Validate()
    {
        var result = new ValidationResult();

        // 检查房间重叠
        for (int i = 0; i < Rooms.Count; i++)
        {
            for (int j = i + 1; j < Rooms.Count; j++)
            {
                if (Rooms[i].OverlapsWith(Rooms[j]))
                {
                    result.AddError($"房间 {Rooms[i].Name} 与 {Rooms[j].Name} 重叠");
                }
            }
        }

        // 检查必要房间类型
        var roomTypes = GetRoomTypeStatistics();
        if (!roomTypes.ContainsKey(RoomType.LivingRoom))
            result.AddWarning("缺少客厅");
        if (!roomTypes.ContainsKey(RoomType.Bedroom))
            result.AddWarning("缺少卧室");
        if (!roomTypes.ContainsKey(RoomType.Kitchen))
            result.AddWarning("缺少厨房");
        if (!roomTypes.ContainsKey(RoomType.Bathroom))
            result.AddWarning("缺少卫生间");

        // 检查面积合理性
        var totalArea = CalculateTotalArea();
        var contourArea = SourceContour?.CalculateArea() ?? 0;
        if (totalArea > contourArea * 1.1f)
            result.AddError("房间总面积超过建筑轮廓面积");

        return result;
    }
}

/// <summary>
/// 表示房间
/// </summary>
public class Room
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public RoomType Type { get; set; }
    public List<Point2D> Boundary { get; set; } = new();
    public float MinArea { get; set; }
    public float MaxArea { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();

    public float CalculateArea()
    {
        if (Boundary.Count < 3) return 0;

        float area = 0;
        for (int i = 0; i < Boundary.Count; i++)
        {
            var current = Boundary[i];
            var next = Boundary[(i + 1) % Boundary.Count];
            area += current.X * next.Y - next.X * current.Y;
        }
        return MathF.Abs(area) / 2.0f;
    }

    public bool OverlapsWith(Room other)
    {
        // 简化的重叠检测，实际应用中需要更复杂的几何算法
        var thisBounds = GetBoundingBox();
        var otherBounds = other.GetBoundingBox();
        return thisBounds.IntersectsWith(otherBounds);
    }

    public RectangleF GetBoundingBox()
    {
        if (Boundary.Count == 0) return RectangleF.Empty;

        var minX = Boundary.Min(p => p.X);
        var maxX = Boundary.Max(p => p.X);
        var minY = Boundary.Min(p => p.Y);
        var maxY = Boundary.Max(p => p.Y);

        return new RectangleF(minX, minY, maxX - minX, maxY - minY);
    }
}

/// <summary>
/// 房间类型枚举
/// </summary>
public enum RoomType
{
    LivingRoom,     // 客厅
    Bedroom,        // 卧室
    Kitchen,        // 厨房
    Bathroom,       // 卫生间
    Balcony,        // 阳台
    Study,          // 书房
    DiningRoom,     // 餐厅
    Storage,        // 储藏室
    Corridor,       // 走廊
    Entrance,       // 玄关
    Other           // 其他
}

/// <summary>
/// 表示墙体
/// </summary>
public class Wall
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Point2D StartPoint { get; set; }
    public Point2D EndPoint { get; set; }
    public float Thickness { get; set; } = 0.2f; // 默认20cm
    public WallType Type { get; set; } = WallType.Interior;
}

public enum WallType
{
    Exterior,   // 外墙
    Interior,   // 内墙
    LoadBearing // 承重墙
}

/// <summary>
/// 表示门
/// </summary>
public class Door
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Point2D Position { get; set; }
    public float Width { get; set; } = 0.9f; // 默认90cm
    public float Angle { get; set; } = 0f; // 旋转角度
    public DoorType Type { get; set; } = DoorType.Interior;
}

public enum DoorType
{
    Interior,   // 室内门
    Entrance,   // 入户门
    Balcony     // 阳台门
}

/// <summary>
/// 表示窗户
/// </summary>
public class Window
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public Point2D Position { get; set; }
    public float Width { get; set; } = 1.2f; // 默认120cm
    public float Height { get; set; } = 1.5f; // 默认150cm
    public float Angle { get; set; } = 0f;
}

/// <summary>
/// 生成参数
/// </summary>
public class GenerationParameters
{
    public int RoomCount { get; set; } = 3;
    public float MinRoomArea { get; set; } = 8.0f; // 最小房间面积8平米
    public bool IncludeBalcony { get; set; } = true;
    public bool IncludeStudy { get; set; } = false;
    public string Style { get; set; } = "Modern"; // 设计风格
    public Dictionary<string, object> CustomParameters { get; set; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public bool IsValid => Errors.Count == 0;

    public void AddError(string error) => Errors.Add(error);
    public void AddWarning(string warning) => Warnings.Add(warning);
}
