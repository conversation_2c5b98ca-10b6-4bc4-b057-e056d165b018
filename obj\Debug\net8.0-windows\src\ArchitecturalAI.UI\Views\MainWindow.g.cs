﻿#pragma checksum "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "ACDFFDD4C011FCBCFAD04F8A04EE8334CCC301F5"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace ArchitecturalAI.UI.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 69 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton SelectTool;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton DrawTool;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton EditTool;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ContourName;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FloorHeight;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FloorNumber;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ApplyProperties;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider RoomCount;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeBalcony;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IncludeStudy;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DesignStyle;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GenerateButton;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomIn;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomOut;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ZoomFit;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GridToggle;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SnapToggle;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer CanvasScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas DrawingCanvas;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 279 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AreaInfo;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RoomInfo;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConfidenceInfo;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveResult;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportImage;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDWG;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CoordinateText;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/ArchitecturalAI;component/src/architecturalai.ui/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 20 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.NewProject_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 21 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.OpenProject_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 22 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveProject_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 24 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Exit_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 27 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Undo_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 28 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.Redo_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 30 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearContour_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 33 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateFloorPlan_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 34 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.BatchGenerate_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 37 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            ((System.Windows.Controls.MenuItem)(target)).Click += new System.Windows.RoutedEventHandler(this.About_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SelectTool = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 12:
            this.DrawTool = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 13:
            this.EditTool = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 14:
            this.ContourName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.FloorHeight = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.FloorNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.ApplyProperties = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ApplyProperties.Click += new System.Windows.RoutedEventHandler(this.ApplyProperties_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RoomCount = ((System.Windows.Controls.Slider)(target));
            return;
            case 19:
            this.IncludeBalcony = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.IncludeStudy = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.DesignStyle = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.GenerateButton = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.GenerateButton.Click += new System.Windows.RoutedEventHandler(this.GenerateFloorPlan_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.ZoomIn = ((System.Windows.Controls.Button)(target));
            
            #line 183 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ZoomIn.Click += new System.Windows.RoutedEventHandler(this.ZoomIn_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.ZoomOut = ((System.Windows.Controls.Button)(target));
            
            #line 187 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ZoomOut.Click += new System.Windows.RoutedEventHandler(this.ZoomOut_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.ZoomFit = ((System.Windows.Controls.Button)(target));
            
            #line 191 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ZoomFit.Click += new System.Windows.RoutedEventHandler(this.ZoomFit_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.GridToggle = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.GridToggle.Click += new System.Windows.RoutedEventHandler(this.GridToggle_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SnapToggle = ((System.Windows.Controls.Button)(target));
            
            #line 200 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.SnapToggle.Click += new System.Windows.RoutedEventHandler(this.SnapToggle_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.CanvasScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 29:
            this.DrawingCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 214 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.DrawingCanvas.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Canvas_MouseLeftButtonDown);
            
            #line default
            #line hidden
            
            #line 215 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.DrawingCanvas.MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.Canvas_MouseLeftButtonUp);
            
            #line default
            #line hidden
            
            #line 216 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.DrawingCanvas.MouseMove += new System.Windows.Input.MouseEventHandler(this.Canvas_MouseMove);
            
            #line default
            #line hidden
            return;
            case 30:
            this.PreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 31:
            this.AreaInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.RoomInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ConfidenceInfo = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.SaveResult = ((System.Windows.Controls.Button)(target));
            
            #line 291 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.SaveResult.Click += new System.Windows.RoutedEventHandler(this.SaveResult_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.ExportImage = ((System.Windows.Controls.Button)(target));
            
            #line 296 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ExportImage.Click += new System.Windows.RoutedEventHandler(this.ExportImage_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.ExportDWG = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\..\..\..\src\ArchitecturalAI.UI\Views\MainWindow.xaml"
            this.ExportDWG.Click += new System.Windows.RoutedEventHandler(this.ExportDWG_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.CoordinateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

