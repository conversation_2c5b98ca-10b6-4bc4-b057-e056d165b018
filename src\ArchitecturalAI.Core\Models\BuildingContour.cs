using System.Drawing;

namespace ArchitecturalAI.Core.Models;

/// <summary>
/// 表示建筑楼层的外轮廓
/// </summary>
public class BuildingContour
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Point2D> Points { get; set; } = new();
    public float FloorHeight { get; set; } = 3.0f; // 默认层高3米
    public int FloorNumber { get; set; } = 1;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime ModifiedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 计算轮廓的面积
    /// </summary>
    public float CalculateArea()
    {
        if (Points.Count < 3) return 0;

        float area = 0;
        for (int i = 0; i < Points.Count; i++)
        {
            var current = Points[i];
            var next = Points[(i + 1) % Points.Count];
            area += current.X * next.Y - next.X * current.Y;
        }
        return MathF.Abs(area) / 2.0f;
    }

    /// <summary>
    /// 计算轮廓的周长
    /// </summary>
    public float CalculatePerimeter()
    {
        if (Points.Count < 2) return 0;

        float perimeter = 0;
        for (int i = 0; i < Points.Count; i++)
        {
            var current = Points[i];
            var next = Points[(i + 1) % Points.Count];
            perimeter += current.DistanceTo(next);
        }
        return perimeter;
    }

    /// <summary>
    /// 获取轮廓的边界框
    /// </summary>
    public RectangleF GetBoundingBox()
    {
        if (Points.Count == 0) return RectangleF.Empty;

        var minX = Points.Min(p => p.X);
        var maxX = Points.Max(p => p.X);
        var minY = Points.Min(p => p.Y);
        var maxY = Points.Max(p => p.Y);

        return new RectangleF(minX, minY, maxX - minX, maxY - minY);
    }

    /// <summary>
    /// 检查轮廓是否闭合
    /// </summary>
    public bool IsClosed()
    {
        if (Points.Count < 3) return false;
        return Points[0] == Points[^1] || Points[0].DistanceTo(Points[^1]) < 0.01f;
    }

    /// <summary>
    /// 简化轮廓（移除冗余点）
    /// </summary>
    public void Simplify(float tolerance = 0.1f)
    {
        if (Points.Count <= 3) return;

        var simplified = new List<Point2D> { Points[0] };
        
        for (int i = 1; i < Points.Count - 1; i++)
        {
            var prev = simplified[^1];
            var current = Points[i];
            var next = Points[i + 1];

            // 计算点到直线的距离
            var distance = PointToLineDistance(current, prev, next);
            if (distance > tolerance)
            {
                simplified.Add(current);
            }
        }

        simplified.Add(Points[^1]);
        Points = simplified;
    }

    private static float PointToLineDistance(Point2D point, Point2D lineStart, Point2D lineEnd)
    {
        var A = point.X - lineStart.X;
        var B = point.Y - lineStart.Y;
        var C = lineEnd.X - lineStart.X;
        var D = lineEnd.Y - lineStart.Y;

        var dot = A * C + B * D;
        var lenSq = C * C + D * D;
        
        if (lenSq == 0) return point.DistanceTo(lineStart);

        var param = dot / lenSq;
        
        Point2D closest;
        if (param < 0)
            closest = lineStart;
        else if (param > 1)
            closest = lineEnd;
        else
            closest = new Point2D(lineStart.X + param * C, lineStart.Y + param * D);

        return point.DistanceTo(closest);
    }

    /// <summary>
    /// 创建轮廓的深拷贝
    /// </summary>
    public BuildingContour Clone()
    {
        return new BuildingContour
        {
            Id = Guid.NewGuid(),
            Name = Name + "_Copy",
            Description = Description,
            Points = new List<Point2D>(Points),
            FloorHeight = FloorHeight,
            FloorNumber = FloorNumber,
            CreatedAt = DateTime.UtcNow,
            ModifiedAt = DateTime.UtcNow
        };
    }
}
