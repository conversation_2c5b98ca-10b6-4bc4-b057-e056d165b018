using ArchitecturalAI.Data.Interfaces;
using ArchitecturalAI.Data.Services;
using ArchitecturalAI.ML.Interfaces;
using ArchitecturalAI.ML.Services;
using ArchitecturalAI.UI.ViewModels;
using ArchitecturalAI.UI.Views;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Windows;

namespace ArchitecturalAI.UI;

public partial class App : Application
{
    private ServiceProvider? _serviceProvider;

    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 配置依赖注入
        var services = new ServiceCollection();
        ConfigureServices(services);
        _serviceProvider = services.BuildServiceProvider();

        // 创建并显示主窗口
        var mainWindow = _serviceProvider.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _serviceProvider?.Dispose();
        base.OnExit(e);
    }

    private void ConfigureServices(IServiceCollection services)
    {
        // 日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddDebug();
        });

        // 核心服务
        services.AddSingleton<IFloorPlanGenerator, OnnxFloorPlanGenerator>();
        services.AddSingleton<IDataCollector, ArchitecturalDataCollector>();

        // ViewModels
        services.AddTransient<MainViewModel>();
        services.AddTransient<ContourEditorViewModel>();
        services.AddTransient<FloorPlanViewerViewModel>();
        services.AddTransient<DataManagementViewModel>();

        // Views
        services.AddTransient<MainWindow>();
    }
}
