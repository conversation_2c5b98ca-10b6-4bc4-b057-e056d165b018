@echo off
echo 建筑平面图AI生成系统 - 构建脚本
echo =====================================

echo.
echo 1. 清理项目...
dotnet clean

echo.
echo 2. 还原NuGet包...
dotnet restore

echo.
echo 3. 构建项目...
dotnet build --configuration Release

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✓ 构建成功完成！
    echo.
    echo 4. 运行应用程序...
    echo   dotnet run
    echo.
    echo 或者直接运行:
    echo   ArchitecturalAI.exe
) else (
    echo.
    echo ✗ 构建失败！
)

echo.
pause
