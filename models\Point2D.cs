namespace ArchitecturalAI.Models;

/// <summary>
/// 表示二维空间中的点
/// </summary>
public struct Point2D
{
    public float X { get; set; }
    public float Y { get; set; }

    public Point2D(float x, float y)
    {
        X = x;
        Y = y;
    }

    public float DistanceTo(Point2D other)
    {
        var dx = X - other.X;
        var dy = Y - other.Y;
        return MathF.Sqrt(dx * dx + dy * dy);
    }

    public override string ToString() => $"({X:F2}, {Y:F2})";
}
