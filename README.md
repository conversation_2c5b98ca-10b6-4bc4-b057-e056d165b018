# 建筑平面图AI生成系统

基于深度学习技术的智能建筑设计工具，能够根据建筑外轮廓自动生成室内平面布置方案。

## 🏗️ 项目概述

本系统采用条件生成对抗网络(Conditional GAN)技术，通过学习大量建筑设计数据，实现从建筑轮廓到平面布置的智能转换。系统提供直观的WPF界面，支持轮廓绘制、参数调整和结果可视化。

### 主要特性

- 🎨 **直观的轮廓绘制界面** - 支持鼠标绘制和编辑建筑外轮廓
- 🤖 **AI智能生成** - 基于深度学习模型自动生成平面布置
- ⚙️ **参数化控制** - 可调整房间数量、设计风格等生成参数
- 📊 **实时预览** - 即时显示生成结果和统计信息
- 💾 **多格式导出** - 支持图片、DWG等多种格式导出
- 🔄 **批量生成** - 一次生成多个设计方案供选择

## 🏛️ 系统架构

```
ArchitecturalAI/
├── src/
│   ├── ArchitecturalAI.Core/        # 核心数据模型和业务逻辑
│   ├── ArchitecturalAI.UI/          # WPF用户界面
│   ├── ArchitecturalAI.ML/          # 机器学习模型集成
│   └── ArchitecturalAI.Data/        # 数据处理和管理
├── training/
│   ├── scripts/                     # 训练脚本
│   └── data/                        # 训练数据
├── models/                          # 预训练模型
├── tests/                           # 单元测试
└── docs/                            # 文档
```

## 🚀 快速开始

### 环境要求

- .NET 8.0 或更高版本
- Windows 10/11 (WPF支持)
- 推荐: NVIDIA GPU (CUDA支持)
- Python 3.8+ (用于模型训练)

### 安装步骤

1. **克隆项目**
   ```bash
   git clone https://github.com/your-repo/ArchitecturalAI.git
   cd ArchitecturalAI
   ```

2. **构建项目**
   ```bash
   dotnet restore
   dotnet build
   ```

3. **运行应用**
   ```bash
   dotnet run --project src/ArchitecturalAI.UI
   ```

### 使用指南

1. **绘制轮廓**
   - 选择"绘制轮廓"工具
   - 在画布上点击绘制建筑外轮廓
   - 右键完成轮廓绘制

2. **设置参数**
   - 调整房间数量滑块
   - 选择设计风格
   - 设置其他生成参数

3. **生成平面图**
   - 点击"生成平面图"按钮
   - 等待AI处理完成
   - 查看生成结果

4. **导出结果**
   - 选择导出格式
   - 保存到指定位置

## 🧠 AI模型训练

### 数据准备

1. **收集训练数据**
   ```bash
   # 使用数据收集器
   dotnet run --project src/ArchitecturalAI.Data -- collect --source ./raw_data --output ./training/data
   ```

2. **数据预处理**
   ```bash
   cd training/scripts
   python preprocess_data.py --input_path ../data/raw --output_path ../data/processed
   ```

### 模型训练

```bash
cd training/scripts
python train_model.py \
    --data_path ../data/processed \
    --output_path ../../models \
    --epochs 100 \
    --batch_size 4
```

### 训练参数说明

- `--epochs`: 训练轮数，建议100-200
- `--batch_size`: 批次大小，根据GPU内存调整
- `--lr`: 学习率，默认0.0002
- `--lambda_l1`: L1损失权重，控制生成质量

## 📊 数据格式

### 轮廓数据格式 (JSON)

```json
{
  "Id": "uuid",
  "Name": "轮廓名称",
  "Points": [
    {"X": 0.0, "Y": 0.0},
    {"X": 100.0, "Y": 0.0},
    {"X": 100.0, "Y": 80.0},
    {"X": 0.0, "Y": 80.0}
  ],
  "FloorHeight": 3.0,
  "FloorNumber": 1
}
```

### 平面图数据格式 (JSON)

```json
{
  "Id": "uuid",
  "ContourId": "contour-uuid",
  "Name": "平面图名称",
  "Rooms": [
    {
      "Id": "room-uuid",
      "Name": "客厅",
      "Type": "LivingRoom",
      "Boundary": [
        {"X": 0.0, "Y": 0.0},
        {"X": 60.0, "Y": 0.0},
        {"X": 60.0, "Y": 40.0},
        {"X": 0.0, "Y": 40.0}
      ]
    }
  ],
  "ConfidenceScore": 0.85
}
```

## 🔧 配置说明

### 模型配置

在 `models/` 目录下放置训练好的ONNX模型文件：
- `floorplan_generator.onnx` - 主生成模型
- `model_metadata.json` - 模型元数据

### 应用配置

主要配置项在 `appsettings.json` 中：

```json
{
  "ModelSettings": {
    "ModelPath": "./models/floorplan_generator.onnx",
    "UseGPU": true,
    "InputSize": 512
  },
  "UISettings": {
    "DefaultCanvasSize": 1000,
    "GridSize": 20,
    "AutoSave": true
  }
}
```

## 🧪 测试

运行单元测试：

```bash
dotnet test
```

运行特定测试：

```bash
dotnet test --filter "TestCategory=Core"
```

## 📈 性能优化

### GPU加速

确保安装CUDA和cuDNN：
- CUDA 11.8+
- cuDNN 8.6+

### 内存优化

- 调整批次大小
- 使用模型量化
- 启用内存映射

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [ONNX Runtime](https://onnxruntime.ai/) - 模型推理引擎
- [SkiaSharp](https://github.com/mono/SkiaSharp) - 图形处理库
- [PyTorch](https://pytorch.org/) - 深度学习框架

## 📞 联系方式

- 项目主页: [GitHub Repository](https://github.com/your-repo/ArchitecturalAI)
- 问题反馈: [Issues](https://github.com/your-repo/ArchitecturalAI/issues)
- 邮箱: <EMAIL>

## 🗺️ 路线图

- [ ] 支持多层建筑
- [ ] 增加更多房间类型
- [ ] 集成BIM数据
- [ ] 移动端支持
- [ ] 云端训练服务
- [ ] 3D可视化

---

**注意**: 这是一个演示项目，实际使用时请根据具体需求调整模型和参数。
