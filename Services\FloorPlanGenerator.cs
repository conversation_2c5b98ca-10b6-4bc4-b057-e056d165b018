using ArchitecturalAI.Models;
using Microsoft.ML.OnnxRuntime;
using System.Drawing;

namespace ArchitecturalAI.Services;

/// <summary>
/// 楼层平面图生成器接口
/// </summary>
public interface IFloorPlanGenerator
{
    Task<FloorPlan> GenerateAsync(BuildingContour contour, GenerationParameters parameters);
    bool IsModelLoaded { get; }
    Task LoadModelAsync(string modelPath);
}

/// <summary>
/// 简化的楼层平面图生成器实现
/// </summary>
public class FloorPlanGenerator : IFloorPlanGenerator, IDisposable
{
    private InferenceSession? _session;
    private bool _disposed = false;

    public bool IsModelLoaded => _session != null;

    public async Task LoadModelAsync(string modelPath)
    {
        if (File.Exists(modelPath))
        {
            try
            {
                _session?.Dispose();
                _session = new InferenceSession(modelPath);
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"加载模型失败: {ex.Message}", ex);
            }
        }
        else
        {
            // 如果模型文件不存在，使用模拟生成
            await Task.CompletedTask;
        }
    }

    public async Task<FloorPlan> GenerateAsync(BuildingContour contour, GenerationParameters parameters)
    {
        // 简化实现：生成模拟的平面图
        await Task.Delay(1000); // 模拟AI处理时间

        var floorPlan = new FloorPlan
        {
            ContourId = contour.Id,
            Name = $"Generated Plan for {contour.Name}",
            SourceContour = contour,
            Parameters = parameters,
            ConfidenceScore = 0.85f
        };

        // 生成示例房间
        GenerateSampleRooms(floorPlan, contour, parameters);

        return floorPlan;
    }

    private void GenerateSampleRooms(FloorPlan floorPlan, BuildingContour contour, GenerationParameters parameters)
    {
        var bounds = contour.GetBoundingBox();
        var roomCount = parameters.RoomCount;

        // 简化的房间生成逻辑
        if (roomCount >= 1)
        {
            // 客厅
            floorPlan.Rooms.Add(new Room
            {
                Name = "客厅",
                Type = RoomType.LivingRoom,
                Boundary = new List<Point2D>
                {
                    new(bounds.X, bounds.Y),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.7f),
                    new(bounds.X, bounds.Y + bounds.Height * 0.7f)
                }
            });
        }

        if (roomCount >= 2)
        {
            // 卧室
            floorPlan.Rooms.Add(new Room
            {
                Name = "主卧",
                Type = RoomType.Bedroom,
                Boundary = new List<Point2D>
                {
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y),
                    new(bounds.X + bounds.Width, bounds.Y),
                    new(bounds.X + bounds.Width, bounds.Y + bounds.Height * 0.5f),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.5f)
                }
            });
        }

        if (roomCount >= 3)
        {
            // 厨房
            floorPlan.Rooms.Add(new Room
            {
                Name = "厨房",
                Type = RoomType.Kitchen,
                Boundary = new List<Point2D>
                {
                    new(bounds.X, bounds.Y + bounds.Height * 0.7f),
                    new(bounds.X + bounds.Width * 0.4f, bounds.Y + bounds.Height * 0.7f),
                    new(bounds.X + bounds.Width * 0.4f, bounds.Y + bounds.Height),
                    new(bounds.X, bounds.Y + bounds.Height)
                }
            });
        }

        if (roomCount >= 4)
        {
            // 卫生间
            floorPlan.Rooms.Add(new Room
            {
                Name = "卫生间",
                Type = RoomType.Bathroom,
                Boundary = new List<Point2D>
                {
                    new(bounds.X + bounds.Width * 0.4f, bounds.Y + bounds.Height * 0.7f),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.7f),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height),
                    new(bounds.X + bounds.Width * 0.4f, bounds.Y + bounds.Height)
                }
            });
        }

        if (parameters.IncludeBalcony && roomCount >= 5)
        {
            // 阳台
            floorPlan.Rooms.Add(new Room
            {
                Name = "阳台",
                Type = RoomType.Balcony,
                Boundary = new List<Point2D>
                {
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height * 0.5f),
                    new(bounds.X + bounds.Width, bounds.Y + bounds.Height * 0.5f),
                    new(bounds.X + bounds.Width, bounds.Y + bounds.Height),
                    new(bounds.X + bounds.Width * 0.6f, bounds.Y + bounds.Height)
                }
            });
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _session?.Dispose();
            _disposed = true;
        }
    }
}
