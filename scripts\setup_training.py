#!/usr/bin/env python3
"""
训练环境设置脚本

这个脚本帮助设置深度学习训练环境，包括：
1. 检查Python环境
2. 安装必要的依赖包
3. 验证GPU支持
4. 创建训练目录结构

使用方法:
    python setup_training.py
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_requirements():
    """安装Python依赖包"""
    print("\n安装Python依赖包...")
    
    requirements = [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "numpy>=1.21.0",
        "pillow>=8.0.0",
        "opencv-python>=4.5.0",
        "matplotlib>=3.3.0",
        "scikit-learn>=1.0.0",
        "tensorboard>=2.8.0",
        "tqdm>=4.60.0"
    ]
    
    for package in requirements:
        print(f"安装 {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")
            return False
    
    return True

def check_gpu_support():
    """检查GPU支持"""
    print("\n检查GPU支持...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ 检测到 {gpu_count} 个GPU")
            print(f"   主GPU: {gpu_name}")
            
            # 检查CUDA版本
            cuda_version = torch.version.cuda
            print(f"   CUDA版本: {cuda_version}")
            
            return True
        else:
            print("⚠️  未检测到CUDA GPU，将使用CPU训练")
            print("   建议安装NVIDIA GPU和CUDA以获得更好的性能")
            return False
    except ImportError:
        print("❌ PyTorch未安装，无法检查GPU支持")
        return False

def create_directory_structure():
    """创建训练目录结构"""
    print("\n创建训练目录结构...")
    
    directories = [
        "training/data/raw",
        "training/data/processed/train/contours",
        "training/data/processed/train/floorplans", 
        "training/data/processed/train/images",
        "training/data/processed/val/contours",
        "training/data/processed/val/floorplans",
        "training/data/processed/val/images",
        "training/data/processed/test/contours",
        "training/data/processed/test/floorplans",
        "training/data/processed/test/images",
        "training/data/processed/statistics",
        "training/logs",
        "training/checkpoints",
        "models",
        "exports"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
    
    return True

def create_sample_config():
    """创建示例配置文件"""
    print("\n创建示例配置文件...")
    
    config_content = """# 训练配置文件示例
# 复制此文件为 training_config.yaml 并根据需要修改

# 数据设置
data:
  raw_path: "./training/data/raw"
  processed_path: "./training/data/processed"
  image_size: 512
  batch_size: 4
  num_workers: 4

# 模型设置
model:
  type: "conditional_gan"
  generator_features: 64
  discriminator_features: 64
  input_channels: 3
  output_channels: 3

# 训练设置
training:
  epochs: 100
  learning_rate: 0.0002
  beta1: 0.5
  beta2: 0.999
  lambda_l1: 100
  save_interval: 10
  log_interval: 50

# 输出设置
output:
  model_path: "./models"
  log_path: "./training/logs"
  checkpoint_path: "./training/checkpoints"
"""
    
    config_path = Path("training/config_example.yaml")
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 创建配置文件: {config_path}")
    return True

def print_next_steps():
    """打印后续步骤"""
    print("\n" + "="*50)
    print("🎉 训练环境设置完成！")
    print("="*50)
    print("\n后续步骤:")
    print("1. 准备训练数据:")
    print("   - 将原始图片放入 training/data/raw/ 目录")
    print("   - 运行数据预处理脚本:")
    print("     python training/scripts/preprocess_data.py --input_path training/data/raw --output_path training/data/processed")
    print()
    print("2. 开始训练:")
    print("   python training/scripts/train_model.py --data_path training/data/processed --output_path models")
    print()
    print("3. 监控训练进度:")
    print("   tensorboard --logdir training/logs")
    print()
    print("4. 训练完成后，ONNX模型将保存在 models/ 目录中")
    print()
    print("📚 更多信息请参考 README.md 和 docs/ 目录中的文档")

def main():
    """主函数"""
    print("建筑平面图AI生成系统 - 训练环境设置")
    print("="*50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        print("\n❌ 依赖包安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 检查GPU支持
    check_gpu_support()
    
    # 创建目录结构
    if not create_directory_structure():
        print("\n❌ 目录创建失败")
        sys.exit(1)
    
    # 创建示例配置
    if not create_sample_config():
        print("\n❌ 配置文件创建失败")
        sys.exit(1)
    
    # 打印后续步骤
    print_next_steps()

if __name__ == "__main__":
    main()
