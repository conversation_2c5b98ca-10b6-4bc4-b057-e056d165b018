{"ModelSettings": {"ModelPath": "./models/floorplan_generator.onnx", "UseGPU": true, "InputSize": 512, "OutputSize": 512, "ModelType": "ConditionalGAN", "ConfidenceThreshold": 0.7}, "UISettings": {"DefaultCanvasSize": 1000, "GridSize": 20, "ShowGrid": true, "SnapToGrid": true, "AutoSave": true, "AutoSaveInterval": 300, "MaxUndoSteps": 50, "DefaultZoomLevel": 1.0, "Theme": "Light"}, "DrawingSettings": {"ContourColor": "#2196F3", "ContourThickness": 2.0, "SelectedColor": "#FF9800", "GridColor": "#E0E0E0", "BackgroundColor": "#FFFFFF", "PointSize": 6, "SnapDistance": 10}, "GenerationSettings": {"DefaultRoomCount": 3, "MinRoomArea": 8.0, "MaxRoomArea": 50.0, "DefaultStyle": "Modern", "IncludeBalcony": true, "IncludeStudy": false, "GenerationTimeout": 30, "BatchSize": 1, "MaxBatchCount": 5}, "DataSettings": {"ProjectsPath": "./projects", "ModelsPath": "./models", "TempPath": "./temp", "ExportPath": "./exports", "BackupPath": "./backups", "MaxFileSize": 10485760, "SupportedImageFormats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"], "SupportedExportFormats": [".png", ".jpg", ".pdf", ".dwg"]}, "PerformanceSettings": {"EnableGPUAcceleration": true, "MaxMemoryUsage": 2048, "ThreadPoolSize": 4, "EnableCaching": true, "CacheSize": 100, "EnableLogging": true, "LogLevel": "Information"}, "ValidationSettings": {"MinContourPoints": 3, "MaxContourPoints": 100, "MinContourArea": 10.0, "MaxContourArea": 10000.0, "ValidateOverlaps": true, "ValidateRoomTypes": true, "ValidateAreaRatios": true}, "ExportSettings": {"DefaultImageFormat": "PNG", "ImageQuality": 95, "ImageDPI": 300, "IncludeMetadata": true, "WatermarkEnabled": false, "WatermarkText": "Generated by ArchitecturalAI", "ScaleToFit": true}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss"}, "File": {"Path": "./logs/app.log", "MaxFileSize": 10485760, "MaxFiles": 10, "IncludeScopes": true}}}