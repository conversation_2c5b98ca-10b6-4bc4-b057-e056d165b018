using ArchitecturalAI.Core.Models;

namespace ArchitecturalAI.ML.Interfaces;

/// <summary>
/// 楼层平面图生成器接口
/// </summary>
public interface IFloorPlanGenerator
{
    /// <summary>
    /// 异步生成楼层平面图
    /// </summary>
    /// <param name="contour">建筑轮廓</param>
    /// <param name="parameters">生成参数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>生成的楼层平面图</returns>
    Task<FloorPlan> GenerateAsync(
        BuildingContour contour, 
        GenerationParameters parameters, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量生成多个方案
    /// </summary>
    /// <param name="contour">建筑轮廓</param>
    /// <param name="parameters">生成参数</param>
    /// <param name="count">生成方案数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>生成的楼层平面图列表</returns>
    Task<List<FloorPlan>> GenerateMultipleAsync(
        BuildingContour contour, 
        GenerationParameters parameters, 
        int count = 3,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查模型是否已加载
    /// </summary>
    bool IsModelLoaded { get; }

    /// <summary>
    /// 加载模型
    /// </summary>
    /// <param name="modelPath">模型文件路径</param>
    Task LoadModelAsync(string modelPath);

    /// <summary>
    /// 卸载模型
    /// </summary>
    void UnloadModel();

    /// <summary>
    /// 获取模型信息
    /// </summary>
    ModelInfo? GetModelInfo();
}

/// <summary>
/// 模型信息
/// </summary>
public class ModelInfo
{
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime TrainedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
    public ModelType Type { get; set; }
    public int InputWidth { get; set; } = 512;
    public int InputHeight { get; set; } = 512;
    public int OutputWidth { get; set; } = 512;
    public int OutputHeight { get; set; } = 512;
}

/// <summary>
/// 模型类型
/// </summary>
public enum ModelType
{
    ConditionalGAN,
    Pix2Pix,
    CycleGAN,
    DiffusionModel,
    Custom
}
