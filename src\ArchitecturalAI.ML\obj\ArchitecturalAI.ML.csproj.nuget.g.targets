﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.managed\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Gpu.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime.gpu\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Gpu.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.ml.onnxruntime\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.ml.onnxruntime\1.16.3\build\netstandard2.0\Microsoft.ML.OnnxRuntime.targets')" />
  </ImportGroup>
</Project>