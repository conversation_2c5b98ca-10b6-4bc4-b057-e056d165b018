Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArchitecturalAI.Core", "src\ArchitecturalAI.Core\ArchitecturalAI.Core.csproj", "{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArchitecturalAI.UI", "src\ArchitecturalAI.UI\ArchitecturalAI.UI.csproj", "{B4E93A30-2D3B-4886-96E7-9C29F6CDE444}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArchitecturalAI.ML", "src\ArchitecturalAI.ML\ArchitecturalAI.ML.csproj", "{8B0B5CC6-9540-4B58-858E-5537A3C25AC4}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArchitecturalAI.Data", "src\ArchitecturalAI.Data\ArchitecturalAI.Data.csproj", "{722C0297-6624-4788-A03E-603DFD11165A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "ArchitecturalAI.Tests", "tests\ArchitecturalAI.Tests\ArchitecturalAI.Tests.csproj", "{6B0FA127-DEAE-4AEE-AB67-1C5F01B2039B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{2A7D0710-C56E-4899-96A5-BB9669D8AE2F}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{51BDBA9F-0D70-4430-A204-81AB4CA92B71}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docs", "docs", "{010ECA2D-F4DA-4FD9-8ADD-ECCA01037E20}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "models", "models", "{C887064E-2501-47B3-A4C3-A1443F914B0A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "training", "training", "{1BCA0B39-3077-44E8-8744-75311E11441F}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4E93A30-2D3B-4886-96E7-9C29F6CDE444}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4E93A30-2D3B-4886-96E7-9C29F6CDE444}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4E93A30-2D3B-4886-96E7-9C29F6CDE444}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4E93A30-2D3B-4886-96E7-9C29F6CDE444}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B0B5CC6-9540-4B58-858E-5537A3C25AC4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B0B5CC6-9540-4B58-858E-5537A3C25AC4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B0B5CC6-9540-4B58-858E-5537A3C25AC4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B0B5CC6-9540-4B58-858E-5537A3C25AC4}.Release|Any CPU.Build.0 = Release|Any CPU
		{722C0297-6624-4788-A03E-603DFD11165A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{722C0297-6624-4788-A03E-603DFD11165A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{722C0297-6624-4788-A03E-603DFD11165A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{722C0297-6624-4788-A03E-603DFD11165A}.Release|Any CPU.Build.0 = Release|Any CPU
		{6B0FA127-DEAE-4AEE-AB67-1C5F01B2039B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6B0FA127-DEAE-4AEE-AB67-1C5F01B2039B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6B0FA127-DEAE-4AEE-AB67-1C5F01B2039B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6B0FA127-DEAE-4AEE-AB67-1C5F01B2039B}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {12345678-1234-1234-1234-123456789ABC}
	EndGlobalSection
EndGlobal
