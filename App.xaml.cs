using System.Windows;

namespace ArchitecturalAI;

public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // 如果命令行参数包含 --test，运行测试
        if (e.Args.Contains("--test"))
        {
            try
            {
                Tests.RunAllTests();
                MessageBox.Show("所有测试通过！", "测试结果", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"测试失败: {ex.Message}", "测试结果", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            Shutdown();
            return;
        }

        base.OnStartup(e);
    }
}
