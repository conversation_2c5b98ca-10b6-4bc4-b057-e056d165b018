#!/usr/bin/env python3
"""
建筑平面图生成模型训练脚本

这个脚本演示了如何训练一个条件生成对抗网络(Conditional GAN)
来根据建筑轮廓生成平面布置图。

使用方法:
    python train_model.py --data_path ./data --output_path ./models --epochs 100

依赖包:
    pip install torch torchvision numpy pillow opencv-python matplotlib tensorboard
"""

import argparse
import os
import json
import numpy as np
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import cv2
import matplotlib.pyplot as plt
from datetime import datetime

class ArchitecturalDataset(Dataset):
    """建筑数据集类"""
    
    def __init__(self, data_path, transform=None, image_size=512):
        self.data_path = Path(data_path)
        self.transform = transform
        self.image_size = image_size
        
        # 加载数据索引
        self.contour_files = list((self.data_path / "contours").glob("*.json"))
        self.floorplan_files = list((self.data_path / "floorplans").glob("*.json"))
        
        print(f"找到 {len(self.contour_files)} 个轮廓文件")
        print(f"找到 {len(self.floorplan_files)} 个平面图文件")
    
    def __len__(self):
        return min(len(self.contour_files), len(self.floorplan_files))
    
    def __getitem__(self, idx):
        # 加载轮廓数据
        contour_data = self.load_contour(self.contour_files[idx])
        contour_image = self.contour_to_image(contour_data)
        
        # 加载对应的平面图数据
        floorplan_data = self.load_floorplan(self.floorplan_files[idx])
        floorplan_image = self.floorplan_to_image(floorplan_data)
        
        if self.transform:
            contour_image = self.transform(contour_image)
            floorplan_image = self.transform(floorplan_image)
        
        return contour_image, floorplan_image
    
    def load_contour(self, file_path):
        """加载轮廓JSON数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def load_floorplan(self, file_path):
        """加载平面图JSON数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def contour_to_image(self, contour_data):
        """将轮廓数据转换为图像"""
        # 创建空白图像
        image = np.zeros((self.image_size, self.image_size, 3), dtype=np.uint8)
        
        # 提取轮廓点
        if 'Points' in contour_data and contour_data['Points']:
            points = [(int(p['X']), int(p['Y'])) for p in contour_data['Points']]
            points = np.array(points, dtype=np.int32)
            
            # 绘制轮廓
            cv2.polylines(image, [points], True, (255, 255, 255), 2)
            cv2.fillPoly(image, [points], (128, 128, 128))
        
        return Image.fromarray(image)
    
    def floorplan_to_image(self, floorplan_data):
        """将平面图数据转换为图像"""
        # 创建空白图像
        image = np.zeros((self.image_size, self.image_size, 3), dtype=np.uint8)
        
        # 绘制房间
        if 'Rooms' in floorplan_data:
            colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0), (255, 0, 255)]
            for i, room in enumerate(floorplan_data['Rooms']):
                if 'Boundary' in room and room['Boundary']:
                    points = [(int(p['X']), int(p['Y'])) for p in room['Boundary']]
                    points = np.array(points, dtype=np.int32)
                    color = colors[i % len(colors)]
                    cv2.fillPoly(image, [points], color)
                    cv2.polylines(image, [points], True, (255, 255, 255), 1)
        
        return Image.fromarray(image)

class Generator(nn.Module):
    """生成器网络"""
    
    def __init__(self, input_channels=3, output_channels=3, features=64):
        super(Generator, self).__init__()
        
        # 编码器
        self.encoder = nn.Sequential(
            self._conv_block(input_channels, features, normalize=False),
            self._conv_block(features, features * 2),
            self._conv_block(features * 2, features * 4),
            self._conv_block(features * 4, features * 8),
            self._conv_block(features * 8, features * 8),
            self._conv_block(features * 8, features * 8),
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            self._upconv_block(features * 8, features * 8),
            self._upconv_block(features * 16, features * 8),
            self._upconv_block(features * 16, features * 4),
            self._upconv_block(features * 8, features * 2),
            self._upconv_block(features * 4, features),
            nn.ConvTranspose2d(features * 2, output_channels, 4, 2, 1),
            nn.Tanh()
        )
    
    def _conv_block(self, in_channels, out_channels, normalize=True):
        layers = [nn.Conv2d(in_channels, out_channels, 4, 2, 1)]
        if normalize:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.LeakyReLU(0.2, inplace=True))
        return nn.Sequential(*layers)
    
    def _upconv_block(self, in_channels, out_channels):
        return nn.Sequential(
            nn.ConvTranspose2d(in_channels, out_channels, 4, 2, 1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        # U-Net架构，保存跳跃连接
        enc_features = []
        
        # 编码
        for layer in self.encoder:
            x = layer(x)
            enc_features.append(x)
        
        # 解码（带跳跃连接）
        for i, layer in enumerate(self.decoder[:-2]):
            x = layer(x)
            if i < len(enc_features) - 1:
                x = torch.cat([x, enc_features[-(i+2)]], dim=1)
        
        # 最后两层
        x = self.decoder[-2](x)
        x = self.decoder[-1](x)
        
        return x

class Discriminator(nn.Module):
    """判别器网络"""
    
    def __init__(self, input_channels=6, features=64):
        super(Discriminator, self).__init__()
        
        self.model = nn.Sequential(
            self._conv_block(input_channels, features, normalize=False),
            self._conv_block(features, features * 2),
            self._conv_block(features * 2, features * 4),
            self._conv_block(features * 4, features * 8),
            nn.Conv2d(features * 8, 1, 4, 1, 1),
            nn.Sigmoid()
        )
    
    def _conv_block(self, in_channels, out_channels, normalize=True):
        layers = [nn.Conv2d(in_channels, out_channels, 4, 2, 1)]
        if normalize:
            layers.append(nn.BatchNorm2d(out_channels))
        layers.append(nn.LeakyReLU(0.2, inplace=True))
        return nn.Sequential(*layers)
    
    def forward(self, contour, floorplan):
        x = torch.cat([contour, floorplan], dim=1)
        return self.model(x)

def train_model(args):
    """训练模型主函数"""
    
    # 设置设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")
    
    # 数据预处理
    transform = transforms.Compose([
        transforms.Resize((args.image_size, args.image_size)),
        transforms.ToTensor(),
        transforms.Normalize([0.5, 0.5, 0.5], [0.5, 0.5, 0.5])
    ])
    
    # 创建数据集和数据加载器
    dataset = ArchitecturalDataset(args.data_path, transform, args.image_size)
    dataloader = DataLoader(dataset, batch_size=args.batch_size, shuffle=True, num_workers=4)
    
    # 创建模型
    generator = Generator().to(device)
    discriminator = Discriminator().to(device)
    
    # 损失函数和优化器
    criterion_gan = nn.BCELoss()
    criterion_l1 = nn.L1Loss()
    
    optimizer_g = optim.Adam(generator.parameters(), lr=args.lr, betas=(0.5, 0.999))
    optimizer_d = optim.Adam(discriminator.parameters(), lr=args.lr, betas=(0.5, 0.999))
    
    # 训练循环
    for epoch in range(args.epochs):
        for i, (contours, floorplans) in enumerate(dataloader):
            contours = contours.to(device)
            floorplans = floorplans.to(device)
            
            batch_size = contours.size(0)
            real_labels = torch.ones(batch_size, 1, 30, 30).to(device)
            fake_labels = torch.zeros(batch_size, 1, 30, 30).to(device)
            
            # 训练判别器
            optimizer_d.zero_grad()
            
            # 真实样本
            real_output = discriminator(contours, floorplans)
            d_real_loss = criterion_gan(real_output, real_labels)
            
            # 生成样本
            fake_floorplans = generator(contours)
            fake_output = discriminator(contours, fake_floorplans.detach())
            d_fake_loss = criterion_gan(fake_output, fake_labels)
            
            d_loss = (d_real_loss + d_fake_loss) / 2
            d_loss.backward()
            optimizer_d.step()
            
            # 训练生成器
            optimizer_g.zero_grad()
            
            fake_output = discriminator(contours, fake_floorplans)
            g_gan_loss = criterion_gan(fake_output, real_labels)
            g_l1_loss = criterion_l1(fake_floorplans, floorplans) * args.lambda_l1
            
            g_loss = g_gan_loss + g_l1_loss
            g_loss.backward()
            optimizer_g.step()
            
            # 打印进度
            if i % 50 == 0:
                print(f"Epoch [{epoch}/{args.epochs}] Batch [{i}/{len(dataloader)}] "
                      f"D_loss: {d_loss.item():.4f} G_loss: {g_loss.item():.4f}")
        
        # 保存模型检查点
        if (epoch + 1) % args.save_interval == 0:
            save_checkpoint(generator, discriminator, epoch, args.output_path)
    
    # 保存最终模型
    save_final_model(generator, args.output_path)

def save_checkpoint(generator, discriminator, epoch, output_path):
    """保存训练检查点"""
    checkpoint = {
        'epoch': epoch,
        'generator_state_dict': generator.state_dict(),
        'discriminator_state_dict': discriminator.state_dict(),
    }
    
    checkpoint_path = Path(output_path) / f"checkpoint_epoch_{epoch+1}.pth"
    torch.save(checkpoint, checkpoint_path)
    print(f"检查点已保存: {checkpoint_path}")

def save_final_model(generator, output_path):
    """保存最终的ONNX模型"""
    generator.eval()
    
    # 创建示例输入
    dummy_input = torch.randn(1, 3, 512, 512)
    
    # 导出为ONNX格式
    onnx_path = Path(output_path) / "floorplan_generator.onnx"
    torch.onnx.export(
        generator,
        dummy_input,
        onnx_path,
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['contour'],
        output_names=['floorplan']
    )
    
    print(f"ONNX模型已保存: {onnx_path}")

def main():
    parser = argparse.ArgumentParser(description="训练建筑平面图生成模型")
    parser.add_argument("--data_path", type=str, required=True, help="训练数据路径")
    parser.add_argument("--output_path", type=str, required=True, help="模型输出路径")
    parser.add_argument("--epochs", type=int, default=100, help="训练轮数")
    parser.add_argument("--batch_size", type=int, default=4, help="批次大小")
    parser.add_argument("--lr", type=float, default=0.0002, help="学习率")
    parser.add_argument("--lambda_l1", type=float, default=100, help="L1损失权重")
    parser.add_argument("--image_size", type=int, default=512, help="图像尺寸")
    parser.add_argument("--save_interval", type=int, default=10, help="保存间隔")
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_path, exist_ok=True)
    
    # 开始训练
    print("开始训练建筑平面图生成模型...")
    print(f"数据路径: {args.data_path}")
    print(f"输出路径: {args.output_path}")
    print(f"训练轮数: {args.epochs}")
    
    train_model(args)
    print("训练完成!")

if __name__ == "__main__":
    main()
