<Window x:Class="ArchitecturalAI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="建筑平面图AI生成系统" Height="700" Width="1000"
        WindowStartupLocation="CenterScreen">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建项目(_N)" Click="NewProject_Click"/>
                <MenuItem Header="保存项目(_S)" Click="SaveProject_Click"/>
                <Separator/>
                <MenuItem Header="退出(_X)" Click="Exit_Click"/>
            </MenuItem>
            <MenuItem Header="生成(_G)">
                <MenuItem Header="生成平面图(_G)" Click="GenerateFloorPlan_Click"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="关于(_A)" Click="About_Click"/>
            </MenuItem>
        </Menu>
        
        <!-- 主工作区 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="5"/>
                <ColumnDefinition Width="250"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧工具面板 -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="工具面板" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        
                        <!-- 绘制工具 -->
                        <GroupBox Header="绘制工具" Margin="0,0,0,10">
                            <StackPanel>
                                <RadioButton Name="SelectTool" Content="选择工具" IsChecked="True" Margin="5"/>
                                <RadioButton Name="DrawTool" Content="绘制轮廓" Margin="5"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 轮廓属性 -->
                        <GroupBox Header="轮廓属性" Margin="0,0,0,10">
                            <StackPanel>
                                <Label Content="名称:"/>
                                <TextBox Name="ContourName" Text="新建轮廓" Margin="0,0,0,5"/>
                                
                                <Label Content="层高(米):"/>
                                <TextBox Name="FloorHeight" Text="3.0" Margin="0,0,0,5"/>
                                
                                <Label Content="楼层号:"/>
                                <TextBox Name="FloorNumber" Text="1" Margin="0,0,0,5"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 生成参数 -->
                        <GroupBox Header="生成参数" Margin="0,0,0,10">
                            <StackPanel>
                                <Label Content="房间数量:"/>
                                <Slider Name="RoomCount" Minimum="2" Maximum="6" Value="3" TickFrequency="1" IsSnapToTickEnabled="True"/>
                                <TextBlock Text="{Binding ElementName=RoomCount, Path=Value}" HorizontalAlignment="Center"/>
                                
                                <CheckBox Name="IncludeBalcony" Content="包含阳台" IsChecked="True" Margin="0,5,0,0"/>
                                <CheckBox Name="IncludeStudy" Content="包含书房" Margin="0,5,0,0"/>
                                
                                <Label Content="设计风格:"/>
                                <ComboBox Name="DesignStyle" SelectedIndex="0" Margin="0,0,0,5">
                                    <ComboBoxItem Content="现代简约"/>
                                    <ComboBoxItem Content="中式传统"/>
                                    <ComboBoxItem Content="欧式古典"/>
                                </ComboBox>
                                
                                <Button Name="GenerateButton" Content="生成平面图" Click="GenerateFloorPlan_Click" 
                                        Background="#4CAF50" Foreground="White" Padding="10,5" Margin="0,10,0,0"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="1" HorizontalAlignment="Stretch" Background="#CCCCCC"/>
            
            <!-- 中央绘图区域 -->
            <Border Grid.Column="2" Background="White" BorderBrush="#CCCCCC" BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 绘图工具栏 -->
                    <ToolBar Grid.Row="0">
                        <Button Name="ClearCanvas" Content="清除" Click="ClearCanvas_Click"/>
                        <Button Name="ZoomFit" Content="适应窗口" Click="ZoomFit_Click"/>
                    </ToolBar>
                    
                    <!-- 绘图画布 -->
                    <ScrollViewer Grid.Row="1" Name="CanvasScrollViewer" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
                        <Canvas Name="DrawingCanvas" Width="800" Height="600"
                                MouseLeftButtonDown="Canvas_MouseLeftButtonDown"
                                MouseMove="Canvas_MouseMove"
                                MouseRightButtonDown="Canvas_MouseRightButtonDown">
                            <!-- 网格背景 -->
                            <Canvas.Background>
                                <DrawingBrush TileMode="Tile" Viewport="0,0,20,20" ViewportUnits="Absolute">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing>
                                            <GeometryDrawing.Geometry>
                                                <RectangleGeometry Rect="0,0,20,20"/>
                                            </GeometryDrawing.Geometry>
                                            <GeometryDrawing.Pen>
                                                <Pen Brush="#F0F0F0" Thickness="0.5"/>
                                            </GeometryDrawing.Pen>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Canvas.Background>
                        </Canvas>
                    </ScrollViewer>
                </Grid>
            </Border>
            
            <!-- 分隔符 -->
            <GridSplitter Grid.Column="3" HorizontalAlignment="Stretch" Background="#CCCCCC"/>
            
            <!-- 右侧结果面板 -->
            <Border Grid.Column="4" Background="#F5F5F5" BorderBrush="#CCCCCC" BorderThickness="1,0,0,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock Text="生成结果" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        
                        <!-- 结果预览 -->
                        <GroupBox Header="预览" Margin="0,0,0,10">
                            <Border Name="PreviewBorder" Height="150" Background="White" BorderBrush="#CCCCCC" BorderThickness="1">
                                <Canvas Name="PreviewCanvas"/>
                            </Border>
                        </GroupBox>
                        
                        <!-- 统计信息 -->
                        <GroupBox Header="统计信息" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Name="AreaInfo" Text="总面积: -- 平方米"/>
                                <TextBlock Name="RoomInfo" Text="房间数量: --"/>
                                <TextBlock Name="ConfidenceInfo" Text="置信度: --%"/>
                            </StackPanel>
                        </GroupBox>
                        
                        <!-- 房间列表 -->
                        <GroupBox Header="房间列表" Margin="0,0,0,10">
                            <ListBox Name="RoomList" Height="120">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="{Binding Name}" Width="60"/>
                                            <TextBlock Text="{Binding Type}" Width="60"/>
                                            <TextBlock Text="{Binding Area, StringFormat='{}{0:F1}m²'}" Width="50"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </GroupBox>
                        
                        <!-- 操作按钮 -->
                        <GroupBox Header="操作" Margin="0,0,0,10">
                            <StackPanel>
                                <Button Name="SaveResult" Content="保存结果" Click="SaveResult_Click" Margin="0,0,0,5"/>
                                <Button Name="ExportImage" Content="导出图片" Click="ExportImage_Click" Margin="0,0,0,5"/>
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="CoordinateText" Text="坐标: (0, 0)" Margin="0,0,10,0"/>
                    <ProgressBar Name="ProgressBar" Width="100" Height="16" Visibility="Collapsed"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
