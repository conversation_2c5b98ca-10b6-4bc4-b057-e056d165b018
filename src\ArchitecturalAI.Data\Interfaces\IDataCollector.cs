using ArchitecturalAI.Core.Models;

namespace ArchitecturalAI.Data.Interfaces;

/// <summary>
/// 数据收集器接口
/// </summary>
public interface IDataCollector
{
    /// <summary>
    /// 收集训练数据
    /// </summary>
    /// <param name="sourcePath">数据源路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task CollectDataAsync(
        string sourcePath, 
        string outputPath, 
        IProgress<DataCollectionProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证数据质量
    /// </summary>
    /// <param name="dataPath">数据路径</param>
    /// <returns>验证结果</returns>
    Task<DataValidationResult> ValidateDataAsync(string dataPath);

    /// <summary>
    /// 获取数据统计信息
    /// </summary>
    /// <param name="dataPath">数据路径</param>
    /// <returns>统计信息</returns>
    Task<DataStatistics> GetStatisticsAsync(string dataPath);
}

/// <summary>
/// 数据预处理器接口
/// </summary>
public interface IDataPreprocessor
{
    /// <summary>
    /// 预处理训练数据
    /// </summary>
    /// <param name="inputPath">输入路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="options">预处理选项</param>
    /// <param name="progress">进度报告</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task PreprocessAsync(
        string inputPath,
        string outputPath,
        PreprocessingOptions options,
        IProgress<PreprocessingProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 数据增强
    /// </summary>
    /// <param name="inputPath">输入路径</param>
    /// <param name="outputPath">输出路径</param>
    /// <param name="augmentationOptions">增强选项</param>
    /// <param name="cancellationToken">取消令牌</param>
    Task AugmentDataAsync(
        string inputPath,
        string outputPath,
        DataAugmentationOptions augmentationOptions,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// 数据收集进度
/// </summary>
public class DataCollectionProgress
{
    public int ProcessedFiles { get; set; }
    public int TotalFiles { get; set; }
    public string CurrentFile { get; set; } = string.Empty;
    public double PercentageComplete => TotalFiles > 0 ? (double)ProcessedFiles / TotalFiles * 100 : 0;
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// 数据验证结果
/// </summary>
public class DataValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public int ValidSamples { get; set; }
    public int InvalidSamples { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// 数据统计信息
/// </summary>
public class DataStatistics
{
    public int TotalSamples { get; set; }
    public int ContourSamples { get; set; }
    public int FloorPlanSamples { get; set; }
    public Dictionary<RoomType, int> RoomTypeDistribution { get; set; } = new();
    public Dictionary<string, double> AreaStatistics { get; set; } = new();
    public Dictionary<string, int> StyleDistribution { get; set; } = new();
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// 预处理选项
/// </summary>
public class PreprocessingOptions
{
    public int TargetImageSize { get; set; } = 512;
    public bool NormalizeImages { get; set; } = true;
    public bool ResizeImages { get; set; } = true;
    public bool ConvertToGrayscale { get; set; } = false;
    public float ValidationSplit { get; set; } = 0.2f;
    public float TestSplit { get; set; } = 0.1f;
    public bool ShuffleData { get; set; } = true;
    public int RandomSeed { get; set; } = 42;
    public Dictionary<string, object> CustomOptions { get; set; } = new();
}

/// <summary>
/// 预处理进度
/// </summary>
public class PreprocessingProgress
{
    public int ProcessedSamples { get; set; }
    public int TotalSamples { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
    public double PercentageComplete => TotalSamples > 0 ? (double)ProcessedSamples / TotalSamples * 100 : 0;
}

/// <summary>
/// 数据增强选项
/// </summary>
public class DataAugmentationOptions
{
    public bool EnableRotation { get; set; } = true;
    public float MaxRotationAngle { get; set; } = 15.0f;
    public bool EnableFlipping { get; set; } = true;
    public bool EnableScaling { get; set; } = true;
    public float MinScale { get; set; } = 0.8f;
    public float MaxScale { get; set; } = 1.2f;
    public bool EnableNoise { get; set; } = false;
    public float NoiseLevel { get; set; } = 0.1f;
    public int AugmentationFactor { get; set; } = 3; // 每个原始样本生成的增强样本数量
}
