<Window
    x:Class="ArchitecturalAI.UI.Views.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    Title="建筑平面图AI生成系统"
    Width="1200"
    Height="800"
    WindowStartupLocation="CenterScreen">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  菜单栏  -->
        <Menu Grid.Row="0">
            <MenuItem Header="文件(_F)">
                <MenuItem Click="NewProject_Click" Header="新建项目(_N)" />
                <MenuItem Click="OpenProject_Click" Header="打开项目(_O)" />
                <MenuItem Click="SaveProject_Click" Header="保存项目(_S)" />
                <Separator />
                <MenuItem Click="Exit_Click" Header="退出(_X)" />
            </MenuItem>
            <MenuItem Header="编辑(_E)">
                <MenuItem Click="Undo_Click" Header="撤销(_U)" />
                <MenuItem Click="Redo_Click" Header="重做(_R)" />
                <Separator />
                <MenuItem Click="ClearContour_Click" Header="清除轮廓(_C)" />
            </MenuItem>
            <MenuItem Header="生成(_G)">
                <MenuItem Click="GenerateFloorPlan_Click" Header="生成平面图(_G)" />
                <MenuItem Click="BatchGenerate_Click" Header="批量生成(_B)" />
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Click="About_Click" Header="关于(_A)" />
            </MenuItem>
        </Menu>

        <!--  主工作区  -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="5" />
                <ColumnDefinition Width="300" />
            </Grid.ColumnDefinitions>

            <!--  左侧工具面板  -->
            <Border
                Grid.Column="0"
                Background="#F5F5F5"
                BorderBrush="#CCCCCC"
                BorderThickness="0,0,1,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock
                            Margin="0,0,0,10"
                            FontSize="16"
                            FontWeight="Bold"
                            Text="工具面板" />

                        <!--  绘制工具  -->
                        <GroupBox Margin="0,0,0,10" Header="绘制工具">
                            <StackPanel>
                                <RadioButton
                                    Name="SelectTool"
                                    Margin="5"
                                    Content="选择工具"
                                    IsChecked="True" />
                                <RadioButton
                                    Name="DrawTool"
                                    Margin="5"
                                    Content="绘制轮廓" />
                                <RadioButton
                                    Name="EditTool"
                                    Margin="5"
                                    Content="编辑点" />
                            </StackPanel>
                        </GroupBox>

                        <!--  轮廓属性  -->
                        <GroupBox Margin="0,0,0,10" Header="轮廓属性">
                            <StackPanel>
                                <Label Content="名称:" />
                                <TextBox
                                    Name="ContourName"
                                    Margin="0,0,0,5"
                                    Text="新建轮廓" />

                                <Label Content="层高(米):" />
                                <TextBox
                                    Name="FloorHeight"
                                    Margin="0,0,0,5"
                                    Text="3.0" />

                                <Label Content="楼层号:" />
                                <TextBox
                                    Name="FloorNumber"
                                    Margin="0,0,0,5"
                                    Text="1" />

                                <Button
                                    Name="ApplyProperties"
                                    Margin="0,5,0,0"
                                    Click="ApplyProperties_Click"
                                    Content="应用属性" />
                            </StackPanel>
                        </GroupBox>

                        <!--  生成参数  -->
                        <GroupBox Margin="0,0,0,10" Header="生成参数">
                            <StackPanel>
                                <Label Content="房间数量:" />
                                <Slider
                                    Name="RoomCount"
                                    IsSnapToTickEnabled="True"
                                    Maximum="8"
                                    Minimum="2"
                                    TickFrequency="1"
                                    Value="3" />
                                <TextBlock HorizontalAlignment="Center" Text="{Binding ElementName=RoomCount, Path=Value}" />

                                <CheckBox
                                    Name="IncludeBalcony"
                                    Margin="0,5,0,0"
                                    Content="包含阳台"
                                    IsChecked="True" />
                                <CheckBox
                                    Name="IncludeStudy"
                                    Margin="0,5,0,0"
                                    Content="包含书房" />

                                <Label Content="设计风格:" />
                                <ComboBox
                                    Name="DesignStyle"
                                    Margin="0,0,0,5"
                                    SelectedIndex="0">
                                    <ComboBoxItem Content="现代简约" />
                                    <ComboBoxItem Content="中式传统" />
                                    <ComboBoxItem Content="欧式古典" />
                                    <ComboBoxItem Content="北欧风格" />
                                </ComboBox>

                                <Button
                                    Name="GenerateButton"
                                    Margin="0,10,0,0"
                                    Padding="10,5"
                                    Background="#4CAF50"
                                    Click="GenerateFloorPlan_Click"
                                    Content="生成平面图"
                                    Foreground="White" />
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!--  分隔符  -->
            <GridSplitter
                Grid.Column="1"
                HorizontalAlignment="Stretch"
                Background="#CCCCCC" />

            <!--  中央绘图区域  -->
            <Border
                Grid.Column="2"
                Background="White"
                BorderBrush="#CCCCCC"
                BorderThickness="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>

                    <!--  绘图工具栏  -->
                    <ToolBar Grid.Row="0">
                        <Button
                            Name="ZoomIn"
                            Click="ZoomIn_Click"
                            Content="放大" />
                        <Button
                            Name="ZoomOut"
                            Click="ZoomOut_Click"
                            Content="缩小" />
                        <Button
                            Name="ZoomFit"
                            Click="ZoomFit_Click"
                            Content="适应窗口" />
                        <Separator />
                        <Button
                            Name="GridToggle"
                            Click="GridToggle_Click"
                            Content="显示网格" />
                        <Button
                            Name="SnapToggle"
                            Click="SnapToggle_Click"
                            Content="对齐网格" />
                    </ToolBar>

                    <!--  绘图画布  -->
                    <ScrollViewer
                        Name="CanvasScrollViewer"
                        Grid.Row="1"
                        HorizontalScrollBarVisibility="Auto"
                        VerticalScrollBarVisibility="Auto">
                        <Canvas
                            Name="DrawingCanvas"
                            Width="1000"
                            Height="800"
                            MouseLeftButtonDown="Canvas_MouseLeftButtonDown"
                            MouseLeftButtonUp="Canvas_MouseLeftButtonUp"
                            MouseMove="Canvas_MouseMove">
                            <!--  网格背景  -->
                            <Canvas.Background>
                                <DrawingBrush
                                    TileMode="Tile"
                                    Viewport="0,0,20,20"
                                    ViewportUnits="Absolute">
                                    <DrawingBrush.Drawing>
                                        <GeometryDrawing>
                                            <GeometryDrawing.Geometry>
                                                <RectangleGeometry Rect="0,0,20,20" />
                                            </GeometryDrawing.Geometry>
                                            <GeometryDrawing.Pen>
                                                <Pen Brush="#E0E0E0" Thickness="0.5" />
                                            </GeometryDrawing.Pen>
                                        </GeometryDrawing>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Canvas.Background>
                        </Canvas>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!--  分隔符  -->
            <GridSplitter
                Grid.Column="3"
                HorizontalAlignment="Stretch"
                Background="#CCCCCC" />

            <!--  右侧结果面板  -->
            <Border
                Grid.Column="4"
                Background="#F5F5F5"
                BorderBrush="#CCCCCC"
                BorderThickness="1,0,0,0">
                <ScrollViewer>
                    <StackPanel Margin="10">
                        <TextBlock
                            Margin="0,0,0,10"
                            FontSize="16"
                            FontWeight="Bold"
                            Text="生成结果" />

                        <!--  结果预览  -->
                        <GroupBox Margin="0,0,0,10" Header="预览">
                            <Border
                                Name="PreviewBorder"
                                Height="200"
                                Background="White"
                                BorderBrush="#CCCCCC"
                                BorderThickness="1">
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Foreground="Gray"
                                    Text="生成的平面图将在这里显示" />
                            </Border>
                        </GroupBox>

                        <!--  统计信息  -->
                        <GroupBox Margin="0,0,0,10" Header="统计信息">
                            <StackPanel>
                                <TextBlock Name="AreaInfo" Text="总面积: -- 平方米" />
                                <TextBlock Name="RoomInfo" Text="房间数量: --" />
                                <TextBlock Name="ConfidenceInfo" Text="置信度: --%" />
                            </StackPanel>
                        </GroupBox>

                        <!--  操作按钮  -->
                        <GroupBox Margin="0,0,0,10" Header="操作">
                            <StackPanel>
                                <Button
                                    Name="SaveResult"
                                    Margin="0,0,0,5"
                                    Click="SaveResult_Click"
                                    Content="保存结果" />
                                <Button
                                    Name="ExportImage"
                                    Margin="0,0,0,5"
                                    Click="ExportImage_Click"
                                    Content="导出图片" />
                                <Button
                                    Name="ExportDWG"
                                    Margin="0,0,0,5"
                                    Click="ExportDWG_Click"
                                    Content="导出DWG" />
                            </StackPanel>
                        </GroupBox>
                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!--  状态栏  -->
        <StatusBar Grid.Row="2">
            <StatusBarItem>
                <TextBlock Name="StatusText" Text="就绪" />
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock
                        Name="CoordinateText"
                        Margin="0,0,10,0"
                        Text="坐标: (0, 0)" />
                    <ProgressBar
                        Name="ProgressBar"
                        Width="100"
                        Height="16"
                        Visibility="Collapsed" />
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
