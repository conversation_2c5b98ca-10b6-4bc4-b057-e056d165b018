using ArchitecturalAI.Core.Models;
using ArchitecturalAI.Data.Interfaces;
using Newtonsoft.Json;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.PixelFormats;
using System.Text.RegularExpressions;

namespace ArchitecturalAI.Data.Services;

/// <summary>
/// 建筑数据收集器
/// </summary>
public class ArchitecturalDataCollector : IDataCollector
{
    private readonly string[] _supportedImageFormats = { ".jpg", ".jpeg", ".png", ".bmp", ".tiff" };
    private readonly string[] _supportedVectorFormats = { ".dwg", ".dxf", ".svg" };

    public async Task CollectDataAsync(
        string sourcePath, 
        string outputPath, 
        IProgress<DataCollectionProgress>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        if (!Directory.Exists(sourcePath))
            throw new DirectoryNotFoundException($"源路径不存在: {sourcePath}");

        Directory.CreateDirectory(outputPath);

        var files = GetAllSupportedFiles(sourcePath);
        var totalFiles = files.Count;
        var processedFiles = 0;

        var contours = new List<BuildingContour>();
        var floorPlans = new List<FloorPlan>();

        foreach (var file in files)
        {
            cancellationToken.ThrowIfCancellationRequested();

            progress?.Report(new DataCollectionProgress
            {
                ProcessedFiles = processedFiles,
                TotalFiles = totalFiles,
                CurrentFile = Path.GetFileName(file),
                Status = "正在处理文件..."
            });

            try
            {
                await ProcessFileAsync(file, contours, floorPlans, cancellationToken);
            }
            catch (Exception ex)
            {
                // 记录错误但继续处理其他文件
                Console.WriteLine($"处理文件 {file} 时出错: {ex.Message}");
            }

            processedFiles++;
        }

        // 保存收集的数据
        await SaveCollectedDataAsync(outputPath, contours, floorPlans);

        progress?.Report(new DataCollectionProgress
        {
            ProcessedFiles = processedFiles,
            TotalFiles = totalFiles,
            Status = "数据收集完成"
        });
    }

    public async Task<DataValidationResult> ValidateDataAsync(string dataPath)
    {
        var result = new DataValidationResult();

        if (!Directory.Exists(dataPath))
        {
            result.Errors.Add($"数据路径不存在: {dataPath}");
            return result;
        }

        var contourFiles = Directory.GetFiles(Path.Combine(dataPath, "contours"), "*.json");
        var floorPlanFiles = Directory.GetFiles(Path.Combine(dataPath, "floorplans"), "*.json");

        result.ValidSamples = 0;
        result.InvalidSamples = 0;

        // 验证轮廓数据
        foreach (var file in contourFiles)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                var contour = JsonConvert.DeserializeObject<BuildingContour>(json);
                
                if (ValidateContour(contour))
                    result.ValidSamples++;
                else
                {
                    result.InvalidSamples++;
                    result.Warnings.Add($"无效的轮廓文件: {Path.GetFileName(file)}");
                }
            }
            catch (Exception ex)
            {
                result.InvalidSamples++;
                result.Errors.Add($"读取轮廓文件失败 {Path.GetFileName(file)}: {ex.Message}");
            }
        }

        // 验证平面图数据
        foreach (var file in floorPlanFiles)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                var floorPlan = JsonConvert.DeserializeObject<FloorPlan>(json);
                
                if (ValidateFloorPlan(floorPlan))
                    result.ValidSamples++;
                else
                {
                    result.InvalidSamples++;
                    result.Warnings.Add($"无效的平面图文件: {Path.GetFileName(file)}");
                }
            }
            catch (Exception ex)
            {
                result.InvalidSamples++;
                result.Errors.Add($"读取平面图文件失败 {Path.GetFileName(file)}: {ex.Message}");
            }
        }

        result.IsValid = result.Errors.Count == 0 && result.ValidSamples > 0;
        result.Details["contour_files"] = contourFiles.Length;
        result.Details["floorplan_files"] = floorPlanFiles.Length;

        return result;
    }

    public async Task<DataStatistics> GetStatisticsAsync(string dataPath)
    {
        var statistics = new DataStatistics();

        if (!Directory.Exists(dataPath))
            return statistics;

        var contourFiles = Directory.GetFiles(Path.Combine(dataPath, "contours"), "*.json");
        var floorPlanFiles = Directory.GetFiles(Path.Combine(dataPath, "floorplans"), "*.json");

        statistics.ContourSamples = contourFiles.Length;
        statistics.FloorPlanSamples = floorPlanFiles.Length;
        statistics.TotalSamples = statistics.ContourSamples + statistics.FloorPlanSamples;

        var areas = new List<double>();
        var roomTypeCounts = new Dictionary<RoomType, int>();
        var styleCounts = new Dictionary<string, int>();

        // 分析轮廓数据
        foreach (var file in contourFiles)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                var contour = JsonConvert.DeserializeObject<BuildingContour>(json);
                if (contour != null)
                {
                    areas.Add(contour.CalculateArea());
                }
            }
            catch
            {
                // 忽略无效文件
            }
        }

        // 分析平面图数据
        foreach (var file in floorPlanFiles)
        {
            try
            {
                var json = await File.ReadAllTextAsync(file);
                var floorPlan = JsonConvert.DeserializeObject<FloorPlan>(json);
                if (floorPlan != null)
                {
                    // 统计房间类型
                    foreach (var room in floorPlan.Rooms)
                    {
                        roomTypeCounts[room.Type] = roomTypeCounts.GetValueOrDefault(room.Type, 0) + 1;
                    }

                    // 统计设计风格
                    var style = floorPlan.Parameters?.Style ?? "Unknown";
                    styleCounts[style] = styleCounts.GetValueOrDefault(style, 0) + 1;
                }
            }
            catch
            {
                // 忽略无效文件
            }
        }

        statistics.RoomTypeDistribution = roomTypeCounts;
        statistics.StyleDistribution = styleCounts;

        if (areas.Count > 0)
        {
            statistics.AreaStatistics["min"] = areas.Min();
            statistics.AreaStatistics["max"] = areas.Max();
            statistics.AreaStatistics["mean"] = areas.Average();
            statistics.AreaStatistics["median"] = areas.OrderBy(x => x).Skip(areas.Count / 2).First();
        }

        return statistics;
    }

    private List<string> GetAllSupportedFiles(string path)
    {
        var files = new List<string>();
        var allExtensions = _supportedImageFormats.Concat(_supportedVectorFormats).ToArray();

        foreach (var extension in allExtensions)
        {
            files.AddRange(Directory.GetFiles(path, $"*{extension}", SearchOption.AllDirectories));
        }

        return files;
    }

    private async Task ProcessFileAsync(
        string filePath, 
        List<BuildingContour> contours, 
        List<FloorPlan> floorPlans,
        CancellationToken cancellationToken)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        var fileName = Path.GetFileNameWithoutExtension(filePath);

        if (_supportedImageFormats.Contains(extension))
        {
            await ProcessImageFileAsync(filePath, contours, floorPlans, cancellationToken);
        }
        else if (_supportedVectorFormats.Contains(extension))
        {
            await ProcessVectorFileAsync(filePath, contours, floorPlans, cancellationToken);
        }
    }

    private async Task ProcessImageFileAsync(
        string filePath, 
        List<BuildingContour> contours, 
        List<FloorPlan> floorPlans,
        CancellationToken cancellationToken)
    {
        try
        {
            using var image = await Image.LoadAsync<Rgb24>(filePath, cancellationToken);
            
            // 根据文件名模式判断是轮廓图还是平面图
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            
            if (IsContourImage(fileName))
            {
                var contour = await ExtractContourFromImageAsync(image, fileName);
                if (contour != null)
                    contours.Add(contour);
            }
            else if (IsFloorPlanImage(fileName))
            {
                var floorPlan = await ExtractFloorPlanFromImageAsync(image, fileName);
                if (floorPlan != null)
                    floorPlans.Add(floorPlan);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理图像文件 {filePath} 失败: {ex.Message}");
        }
    }

    private async Task ProcessVectorFileAsync(
        string filePath, 
        List<BuildingContour> contours, 
        List<FloorPlan> floorPlans,
        CancellationToken cancellationToken)
    {
        // 这里需要实现CAD文件解析
        // 由于复杂性，这里提供一个简化的实现框架
        await Task.CompletedTask;
        
        // TODO: 实现DWG/DXF文件解析
        // 可以使用第三方库如 netDxf 或 Open Design Alliance
    }

    private bool IsContourImage(string fileName)
    {
        var contourPatterns = new[] { "contour", "outline", "boundary", "轮廓", "边界" };
        return contourPatterns.Any(pattern => 
            fileName.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsFloorPlanImage(string fileName)
    {
        var planPatterns = new[] { "plan", "layout", "floorplan", "平面图", "布局" };
        return planPatterns.Any(pattern => 
            fileName.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private async Task<BuildingContour?> ExtractContourFromImageAsync(Image<Rgb24> image, string name)
    {
        // 这里需要实现图像处理算法来提取轮廓
        // 简化实现，实际需要使用计算机视觉算法
        
        var contour = new BuildingContour
        {
            Name = name,
            Description = $"从图像 {name} 提取的轮廓"
        };

        // 模拟轮廓点提取
        var width = image.Width;
        var height = image.Height;
        
        contour.Points.AddRange(new[]
        {
            new Point2D(width * 0.1f, height * 0.1f),
            new Point2D(width * 0.9f, height * 0.1f),
            new Point2D(width * 0.9f, height * 0.9f),
            new Point2D(width * 0.1f, height * 0.9f)
        });

        await Task.CompletedTask;
        return contour;
    }

    private async Task<FloorPlan?> ExtractFloorPlanFromImageAsync(Image<Rgb24> image, string name)
    {
        // 这里需要实现图像分析算法来提取平面图信息
        // 简化实现
        
        var floorPlan = new FloorPlan
        {
            Name = name,
            Description = $"从图像 {name} 提取的平面图"
        };

        await Task.CompletedTask;
        return floorPlan;
    }

    private async Task SaveCollectedDataAsync(
        string outputPath, 
        List<BuildingContour> contours, 
        List<FloorPlan> floorPlans)
    {
        var contoursPath = Path.Combine(outputPath, "contours");
        var floorPlansPath = Path.Combine(outputPath, "floorplans");
        
        Directory.CreateDirectory(contoursPath);
        Directory.CreateDirectory(floorPlansPath);

        // 保存轮廓数据
        for (int i = 0; i < contours.Count; i++)
        {
            var json = JsonConvert.SerializeObject(contours[i], Formatting.Indented);
            var fileName = $"contour_{i:D6}.json";
            await File.WriteAllTextAsync(Path.Combine(contoursPath, fileName), json);
        }

        // 保存平面图数据
        for (int i = 0; i < floorPlans.Count; i++)
        {
            var json = JsonConvert.SerializeObject(floorPlans[i], Formatting.Indented);
            var fileName = $"floorplan_{i:D6}.json";
            await File.WriteAllTextAsync(Path.Combine(floorPlansPath, fileName), json);
        }

        // 保存元数据
        var metadata = new
        {
            collected_at = DateTime.UtcNow,
            contour_count = contours.Count,
            floorplan_count = floorPlans.Count,
            total_samples = contours.Count + floorPlans.Count
        };

        var metadataJson = JsonConvert.SerializeObject(metadata, Formatting.Indented);
        await File.WriteAllTextAsync(Path.Combine(outputPath, "metadata.json"), metadataJson);
    }

    private bool ValidateContour(BuildingContour? contour)
    {
        return contour != null && 
               contour.Points.Count >= 3 && 
               contour.CalculateArea() > 0;
    }

    private bool ValidateFloorPlan(FloorPlan? floorPlan)
    {
        return floorPlan != null && 
               floorPlan.Rooms.Count > 0 &&
               floorPlan.Rooms.All(r => r.CalculateArea() > 0);
    }
}
