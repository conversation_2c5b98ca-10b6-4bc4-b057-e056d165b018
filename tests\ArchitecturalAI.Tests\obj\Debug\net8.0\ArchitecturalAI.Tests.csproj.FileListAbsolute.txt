D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\CoverletSourceRootsMapping_ArchitecturalAI.Tests
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.csproj.AssemblyReference.cache
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.GeneratedMSBuildEditorConfig.editorconfig
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.AssemblyInfoInputs.cache
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.AssemblyInfo.cs
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.csproj.CoreCompileInputs.cache
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\testhost.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\testhost.exe
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.runner.visualstudio.dotnetcore.testadapter.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.runner.reporters.netcoreapp10.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.runner.utility.netcoreapp10.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Tests.deps.json
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Tests.runtimeconfig.json
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Tests.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Tests.pdb
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Castle.Core.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\CsvHelper.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.VisualStudio.CodeCoverage.Shim.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.ML.OnnxRuntime.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.TestPlatform.CoreUtilities.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.TestPlatform.PlatformAbstractions.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.VisualStudio.TestPlatform.ObjectModel.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.TestPlatform.CommunicationUtilities.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.TestPlatform.CrossPlatEngine.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.TestPlatform.Utilities.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.VisualStudio.TestPlatform.Common.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Microsoft.Win32.SystemEvents.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Moq.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\Newtonsoft.Json.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\NuGet.Frameworks.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\SkiaSharp.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\System.Diagnostics.EventLog.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\System.Drawing.Common.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.abstractions.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.assert.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.core.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\xunit.execution.dotnet.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\cs\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\cs\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\de\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\de\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\es\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\es\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\fr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\fr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\it\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\it\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ja\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ja\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ko\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ko\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pl\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pl\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pt-BR\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pt-BR\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ru\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ru\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\tr\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\tr\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hans\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hant\Microsoft.TestPlatform.CoreUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\cs\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\cs\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\cs\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\de\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\de\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\de\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\es\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\es\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\es\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\fr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\fr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\fr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\it\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\it\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\it\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ja\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ja\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ja\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ko\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ko\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ko\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pl\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pl\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pl\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pt-BR\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pt-BR\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\pt-BR\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ru\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ru\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ru\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\tr\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\tr\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\tr\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hans\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hans\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hans\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hant\Microsoft.TestPlatform.CommunicationUtilities.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hant\Microsoft.TestPlatform.CrossPlatEngine.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\zh-Hant\Microsoft.VisualStudio.TestPlatform.Common.resources.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\android\native\onnxruntime.aar
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\Info.plist
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\coreml_provider_factory.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\cpu_provider_factory.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_c_api.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_cxx_api.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_cxx_inline.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Headers\onnxruntime_float16.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\Info.plist
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64\onnxruntime.framework\onnxruntime
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\coreml_provider_factory.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\cpu_provider_factory.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_c_api.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_cxx_api.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_cxx_inline.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Headers\onnxruntime_float16.h
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\Info.plist
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\ios\native\onnxruntime.xcframework\ios-arm64_x86_64-simulator\onnxruntime.framework\onnxruntime
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\linux-arm64\native\libonnxruntime.so
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime.so
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\osx-arm64\native\libonnxruntime.dylib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\osx-x64\native\libonnxruntime.dylib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm\native\onnxruntime.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm\native\onnxruntime.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm\native\onnxruntime_providers_shared.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm\native\onnxruntime_providers_shared.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm64\native\onnxruntime_providers_shared.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_shared.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x86\native\onnxruntime_providers_shared.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_cuda.so
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_shared.so
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\linux-x64\native\libonnxruntime_providers_tensorrt.so
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_cuda.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_cuda.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_tensorrt.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\onnxruntime_providers_tensorrt.lib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win\lib\net8.0\Microsoft.Win32.SystemEvents.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\osx\native\libSkiaSharp.dylib
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-arm64\native\libSkiaSharp.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x64\native\libSkiaSharp.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win-x86\native\libSkiaSharp.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.Messages.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\runtimes\win\lib\net6.0\System.Diagnostics.EventLog.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Core.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Data.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.ML.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Core.pdb
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.ML.pdb
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\bin\Debug\net8.0\ArchitecturalAI.Data.pdb
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\Architec.D821551D.Up2Date
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\refint\ArchitecturalAI.Tests.dll
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.pdb
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ArchitecturalAI.Tests.genruntimeconfig.cache
D:\MyWork\12AIAgent\02Augment\01Code\02AI训练\tests\ArchitecturalAI.Tests\obj\Debug\net8.0\ref\ArchitecturalAI.Tests.dll
